<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.crm</groupId>
	<artifactId>crm-dubbo-framework</artifactId>
	<version>2.0.7.1-RELEASE</version>
	<packaging>pom</packaging>
	<name>Dubbo Framework </name>

	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<dubbo.version>2.5.3</dubbo.version>
		<zkclient.version>0.4</zkclient.version>
		<junit.version>4.13.2</junit.version>
		<pa.framework.version>1.0.1-SNAPSHOT</pa.framework.version>
		<pa.framework.msg.version>1.0.2-SNAPSHOT</pa.framework.msg.version>
		<h2.version>1.3.176</h2.version>
		<howbuy.cache.version>3.0-RELEASE</howbuy.cache.version>
		<hbone.version>2.5.0-SNAPSHOT</hbone.version>
		<com.howbuy.cs-task-client.version>2.0.7.1-RELEASE</com.howbuy.cs-task-client.version>
		<com.howbuy.message-public-client.version>5.1.17-RELEASE</com.howbuy.message-public-client.version>
		<com.howbuy.crm-core-client.version>2.0.0.7-RELEASE</com.howbuy.crm-core-client.version>
		<com.howbuy.acc-center-facade.version>3.6.6-RELEASE</com.howbuy.acc-center-facade.version>
		<com.howbuy.howbuy-auth-facade.version>2.2.0-RELEASE</com.howbuy.howbuy-auth-facade.version>
	<com.howbuy.crm-account-client.version>bugfix20250828-RELEASE</com.howbuy.crm-account-client.version>
<com.howbuy.crm-nt-client.version>2.0.5.7-RELEASE</com.howbuy.crm-nt-client.version>
<com.howbuy.crm-account.version>2.0.0.3-RELEASE</com.howbuy.crm-account.version>
		<com.howbuy.howbuy-dfile-service.version>1.12.0-RELEASE</com.howbuy.howbuy-dfile-service.version>
		<com.howbuy.howbuy-dfile-impl-nfs.version>1.12.0-RELEASE</com.howbuy.howbuy-dfile-impl-nfs.version>
<com.howbuy.crm-td-client.version>1.9.6.5-RELEASE</com.howbuy.crm-td-client.version>
<com.howbuy.howbuy-member-client.version>bugfix-*********-addFix-RELEASE</com.howbuy.howbuy-member-client.version>
<com.howbuy.high-order-center-client.version>********-001-RELEASE</com.howbuy.high-order-center-client.version>
</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-service</artifactId>
				<version>${com.howbuy.howbuy-dfile-service.version}</version>
			</dependency>

			<!--<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-nfs</artifactId>
				<version>${com.howbuy.howbuy-dfile-impl-nfs.version}</version>
			</dependency>-->

			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-webdav</artifactId>
				<version>${com.howbuy.howbuy-dfile-service.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.pa.framework</groupId>
				<artifactId>howbuy-framework-plugin-msg</artifactId>
				<version>${pa.framework.msg.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.pa.framework</groupId>
				<artifactId>howbuy-framework-rpc</artifactId>
				<version>2.0.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.pa.framework</groupId>
				<artifactId>howbuy-framework-rdbms</artifactId>
				<version>${pa.framework.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.pa.framework</groupId>
				<artifactId>howbuy-framework-rdbms-oracle</artifactId>
				<version>${pa.framework.version}</version>
			</dependency>

			<dependency>
				<groupId>com.101tec</groupId>
				<artifactId>zkclient</artifactId>
				<version>${zkclient.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>spring</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>commons-logging</groupId>
				<artifactId>commons-logging</artifactId>
				<version>1.2</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuyUtil</artifactId>
				<version>1.0.3-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.pa.cache</groupId>
				<artifactId>howbuy-cache-client</artifactId>
				<version>${howbuy.cache.version}</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<repositories>
		<repository>
			<id>nexus_howbuy</id>
			<name>nexus_howbuy</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
		</repository>
	</repositories>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>
	<modules>
		<module>cs-task-server</module>
	</modules>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.5</version>
			</plugin>
		</plugins>
	</build>

</project>