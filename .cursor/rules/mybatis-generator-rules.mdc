---
description: 根据数据库表结构生成符合CS-Task-Server项目规范的MyBatis Mapper和Model类代码
globs: ["**/task/**/*.java", "**/task/**/*.xml", "*.sql"]
alwaysApply: false
---

# CS-Task-Server项目MyBatis代码生成规范

本规范用于指导基于数据库表结构生成符合CS-Task-Server项目规范的MyBatis Mapper和Model类代码。

## 1. 项目架构概述

CS-Task-Server是基于Dubbo的分布式CRM任务处理服务，使用Java 1.8 + MyBatis + Oracle架构，专注于客户任务处理和数据同步业务。

## 2. 目录结构规范

### 2.1 Model类目录结构
```
cs-task-server/src/main/java/com/howbuy/cs/task/model/表名Model.java
```

### 2.2 Mapper接口目录结构
```
cs-task-server/src/main/java/com/howbuy/cs/task/dao/表名Mapper.java
```

### 2.3 Mapper XML目录结构
```
cs-task-server/src/main/java/com/howbuy/cs/task/mapper/表名Mapper.xml
```

## 3. 命名规范

### 3.1 Model类命名规范
- 类名：表名转驼峰命名法（不加后缀）
- 例如：表名为SYNC_BP_FUND_BASIC_INFO，对应的Model类名为SyncBpFundBasicInfo
- 不使用任何注解（不使用@Data、@Getter、@Setter），使用传统getter/setter方法

### 3.2 Mapper接口命名规范  
- 接口名：表名转驼峰命名法 + Mapper后缀
- 例如：表名为SYNC_BP_FUND_BASIC_INFO，对应的Mapper接口名为SyncBpFundBasicInfoMapper
- 使用@MapperScan注解

### 3.3 Mapper XML命名规范
- XML文件名：表名转驼峰命名法 + Mapper.xml
- 例如：表名为SYNC_BP_FUND_BASIC_INFO，对应的XML文件名为SyncBpFundBasicInfoMapper.xml

## 4. Model类代码规范

### 4.1 Model类基本结构
```java
package com.howbuy.cs.task.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 表注释/业务描述
 */
public class 表名Model {
    /**
     * 字段注释
     */
    private 字段类型 字段名;
    
    // getter/setter方法
    public 字段类型 get字段名() {
        return 字段名;
    }
    
    public void set字段名(字段类型 字段名) {
        this.字段名 = 字段名;
    }
}
```

### 4.2 Model类字段命名规范
- 字段名：数据库字段名转小驼峰命名法
- 例如：数据库字段名为FUND_CODE，对应的Java字段名为fundCode

### 4.3 Model类字段类型映射规范
- VARCHAR/CHAR -> String
- BIGINT -> Long  
- INTEGER/INT -> Integer
- DECIMAL/NUMBER -> BigDecimal
- TIMESTAMP -> Date
- DATE -> Date (使用java.util.Date)

### 4.4 Model类注释规范
- 类注释：使用数据库表的业务描述
- 字段注释：使用数据库表字段的注释
- 不需要添加@author和@date等详细注释信息

## 5. Mapper接口代码规范

### 5.1 Mapper接口基本结构
```java
package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.表名Model;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface 表名Mapper {
    int deleteByPrimaryKey(主键类型 主键名);
    
    int insert(表名Model record);
    
    int insertSelective(表名Model record);
    
    表名Model selectByPrimaryKey(主键类型 主键名);
    
    int updateByPrimaryKeySelective(表名Model record);
    
    int updateByPrimaryKey(表名Model record);
    
}
```

### 5.2 自定义方法注释规范
- 自定义业务方法需要添加完整的注释
- 注释作者统一为：hongdong.xie
- 日期格式：当前时间，格式为yyyy/MM/dd HH:mm或yyyy-MM-dd HH:mm:ss

## 6. Mapper XML代码规范

### 6.1 Mapper XML基本结构
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.表名Mapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.表名Model">
    <!--@mbg.generated-->
    <!--@Table 数据库表名-->
    <id column="主键列名" jdbcType="主键列类型" property="主键属性名" />
    <result column="列名1" jdbcType="列类型1" property="属性名1" />
    <result column="列名2" jdbcType="列类型2" property="属性名2" />
    <!-- 其他列映射... -->
  </resultMap>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    列名1, 列名2, 列名3, ...
  </sql>
  
  <!-- 基础CRUD操作 -->
  <select id="selectByPrimaryKey" parameterType="主键Java类型" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from 数据库表名
    where 主键列名 = #{主键属性名,jdbcType=主键列类型}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="主键Java类型">
    <!--@mbg.generated-->
    delete from 数据库表名
    where 主键列名 = #{主键属性名,jdbcType=主键列类型}
  </delete>
  
  <insert id="insert" parameterType="com.howbuy.cs.task.model.表名Model">
    <!--@mbg.generated-->
    insert into 数据库表名 (列名1, 列名2, ...)
    values (#{属性名1,jdbcType=列类型1}, #{属性名2,jdbcType=列类型2}, ...)
  </insert>
  
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.表名Model">
    <!--@mbg.generated-->
    insert into 数据库表名
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="属性名1 != null">
        列名1,
      </if>
      <!-- 其他字段... -->
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="属性名1 != null">
        #{属性名1,jdbcType=列类型1},
      </if>
      <!-- 其他字段... -->
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.表名Model">
    <!--@mbg.generated-->
    update 数据库表名
    <set>
      <if test="属性名1 != null">
        列名1 = #{属性名1,jdbcType=列类型1},
      </if>
      <!-- 其他字段... -->
    </set>
    where 主键列名 = #{主键属性名,jdbcType=主键列类型}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.表名Model">
    <!--@mbg.generated-->
    update 数据库表名
    set 列名1 = #{属性名1,jdbcType=列类型1},
        列名2 = #{属性名2,jdbcType=列类型2},
        <!-- 其他字段... -->
    where 主键列名 = #{主键属性名,jdbcType=主键列类型}
  </update>
  
</mapper>
```

### 6.2 JDBC类型映射规范
- VARCHAR -> VARCHAR
- CHAR -> CHAR  
- BIGINT -> BIGINT
- INTEGER -> INTEGER
- DECIMAL -> DECIMAL
- NUMBER -> DECIMAL
- TIMESTAMP -> TIMESTAMP
- DATE -> DATE

### 6.3 Oracle特殊语法
- 批量插入使用Oracle的`insert all ... select 1 from dual`语法
- 序列使用：`DUAL表名_SEQ.NEXTVAL`
- 日期函数使用：`SYSDATE`、`TO_DATE`等Oracle函数

## 7. 项目特殊规范

### 7.1 包结构特点
- Model类统一放在`com.howbuy.cs.task.model`包下
- Mapper接口统一放在`com.howbuy.cs.task.dao`包下  
- Mapper XML文件放在`src/main/java/com/howbuy/cs/task/mapper/`目录下

### 7.2 注解规范
- Model类不使用Lombok注解，使用传统getter/setter
- Mapper接口使用`@MapperScan`注解
- 参数使用`@Param`注解标注

### 7.3 业务特点
- 主要处理客户数据同步和任务处理业务
- 大量涉及基金、客户、投顾相关的数据表
- 需要支持批量操作和Oracle特定语法

## 8. 代码生成步骤

### 8.1 生成Model类
1. 根据数据库表结构生成对应的Model类
2. 类名采用表名转驼峰命名法（不加后缀）
3. 字段名采用数据库字段转小驼峰命名法
4. 使用传统getter/setter方法，不使用注解
5. 添加表和字段的业务注释

### 8.2 生成Mapper接口
1. 接口名采用表名转驼峰命名法 + Mapper后缀
2. 实现标准的CRUD方法
3. 添加`@MapperScan`注解
4. 自定义业务方法需要添加完整注释

### 8.3 生成Mapper XML
1. XML文件名与Mapper接口名保持一致
2. 实现所有接口方法的SQL映射
3. 使用Oracle数据库语法
4. 批量操作使用Oracle的insert all语法
5. 添加`<!--@mbg.generated-->`注释标记

## 9. 示例代码

### 9.1 Model类示例
```java
package com.howbuy.cs.task.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 基金基本信息表
 */
public class SyncBpFundBasicInfo {
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 基金名称
     */
    private String fundName;
    
    /**
     * 基金面值
     */
    private BigDecimal faceValue;
    
    /**
     * 同步时间
     */
    private Date syncDate;
    
    public String getFundCode() {
        return fundCode;
    }
    
    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }
    
    // 其他getter/setter方法...
}
```

### 9.2 Mapper接口示例
```java
package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.SyncBpFundBasicInfo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface SyncBpFundBasicInfoMapper {
    int deleteByPrimaryKey(String fundCode);
    
    int insert(SyncBpFundBasicInfo record);
    
    int insertSelective(SyncBpFundBasicInfo record);
    
    SyncBpFundBasicInfo selectByPrimaryKey(String fundCode);
    
    int updateByPrimaryKeySelective(SyncBpFundBasicInfo record);
    
    int updateByPrimaryKey(SyncBpFundBasicInfo record);
}
```

## 10. 注意事项

1. 确保生成的代码符合项目现有风格
2. Model类不使用任何Lombok注解，保持传统Java风格
3. Mapper XML使用Oracle数据库特定语法
4. 自定义方法必须添加完整的javadoc注释
5. 批量操作使用Oracle的insert all语法
6. 日期字段统一使用java.util.Date类型
7. 字符串判空使用StringUtils.isEmpty()
8. 遵循项目整体的编码规范和架构设计
9. 确保生成的代码可以正常编译和运行
10. 保持与现有代码风格的一致性