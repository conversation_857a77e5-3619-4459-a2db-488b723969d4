---
description: 外部Dubbo接口封装规范
globs: ["**/outservice/**/*.java"]
alwaysApply:false
---
# 外部Dubbo接口封装规范

## 1. 概述

本规范定义了CS-Task-Server项目中外部Dubbo接口封装的标准化要求，确保外部接口调用的统一性、可维护性和安全性。

### 1.1 核心原则

- **封装隔离**: 外部dubbo接口的请求参数和返回参数对象不能直接给业务代码使用，必须进行封装转换
- **参数聚合**: 当方法参数超过4个时，必须创建Context类进行聚合
- **返回封装**: 当返回参数超过一个字段时，必须创建DTO类进行封装
- **统一异常**: 统一异常处理和日志记录规范

## 2. 包结构规范

### 2.1 目录结构

```
com.howbuy.cs.outservice
├── {外部系统名}                    // 外部系统包，如auth、crm、accenter等
│   ├── {具体服务名}OuterService.java // 外部服务封装类
│   └── domain                      // 领域对象包
│       ├── {业务名}Context.java    // 上下文参数类（入参聚合）
│       └── {业务名}DTO.java        // 数据传输对象（返回值封装）
```

### 2.2 包命名规范

- 外部系统包名：使用外部系统的简称，全小写，如 `auth`、`crm`、`accenter`
- domain子包：固定命名为 `domain`
- 包内可根据业务模块再细分子包，如 `crm.core`、`crm.message`等

## 3. 类命名规范

### 3.1 外部服务封装类

- **命名格式**: `{业务模块名}OuterService`
- **示例**: `EncryptOuterService`、`SendMessageOuterService`、`QueryAccCenterOuterService`

### 3.2 上下文参数类（Context）

- **命名格式**: `{业务操作名}Context`
- **使用条件**: 方法入参超过4个时使用
- **示例**: `UserQueryContext`、`OrderCreateContext`

### 3.3 数据传输对象（DTO）

- **命名格式**: `{业务模块名}DTO`
- **使用条件**: 返回参数超过一个字段时使用
- **示例**: `UserInfoDTO`、`OrderResultDTO`

## 4. 参数封装规则

### 4.1 入参封装规则

- **≤4个参数**: 可直接使用基础类型作为方法参数
- **>4个参数**: 必须创建Context类聚合参数

### 4.2 Context类规范

```java
/**
 * @description: 用户查询上下文参数
 * <AUTHOR>
 * @date 2025-09-07 10:24:47
 * @since JDK 1.8
 */
@Getter
@Setter
public class UserQueryContext implements Serializable {
  
    private static final long serialVersionUID = 1L;
  
    /**
     * 用户ID
     */
    private String userId;
  
    /**
     * 用户名
     */
    private String userName;
  
    /**
     * 开始时间
     */
    private Date startTime;
  
    /**
     * 结束时间
     */
    private Date endTime;
  
    /**
     * 状态
     */
    private String status;
}
```

## 5. 返回值封装规则

### 5.1 返回值封装规则

- **单个基础类型**: 可直接返回，如 `String`、`Integer`、`Boolean`
- **多个字段**: 必须创建DTO类封装返回

### 5.2 DTO类规范

```java
/**
 * @description: 用户信息数据传输对象
 * <AUTHOR>  
 * @date 2025-09-07 10:24:47
 * @since JDK 1.8
 */
@Getter
@Setter
public class UserInfoDTO implements Serializable {
  
    private static final long serialVersionUID = 1L;
  
    /**
     * 用户ID
     */
    private String userId;
  
    /**
     * 用户名
     */
    private String userName;
  
    /**
     * 邮箱
     */
    private String email;
  
    /**
     * 状态
     */
    private String status;
}
```

## 6. 外部服务封装类规范

### 6.1 类注解规范

```java
/**
 * @description: 外部认证服务封装
 * <AUTHOR>
 * @date 2025-09-07 10:24:47
 * @since JDK 1.8
 */
@Slf4j
@Service
public class AuthOuterService {
    // 实现内容
}
```

### 6.2 依赖注入规范

- 使用 `@Autowired`注解注入外部Dubbo接口
- 外部接口变量命名使用驼峰命名法
- 禁止在外部服务封装类中直接使用外部接口的请求/响应对象暴露给业务层
- com.howbuy.common.exception.BusinessException

### 6.3 方法实现规范

```java
/**
 * @description: 用户信息查询
 * @param context 查询上下文参数
 * @return UserInfoDTO 用户信息DTO
 * <AUTHOR>
 * @date 2025-09-07 10:24:47
 * @since JDK 1.8
 */
public UserInfoDTO queryUserInfo(UserQueryContext context) {
    // 参数校验
    if (context == null || StringUtils.isEmpty(context.getUserId())) {
        throw new BusinessException("", "查询参数不能为空");
    }
  
    try {
        // 构建外部接口请求对象
        ExternalUserQueryRequest request = new ExternalUserQueryRequest();
        request.setUserId(context.getUserId());
        request.setUserName(context.getUserName());
        // ... 其他参数转换
  
        // 调用外部接口
        ExternalUserQueryResponse response = externalUserFacade.queryUser(request);
  
        // 响应结果校验
        if (response == null || !isSuccess(response)) {
            log.error("外部接口调用失败，request={}, response={}", 
                JSON.toJSONString(request), JSON.toJSONString(response));
            throw new BusinessException("", "用户信息查询失败");
        }
  
        // 转换为内部DTO对象
        return convertToUserInfoDTO(response);
  
    } catch (Exception e) {
        log.error("用户信息查询异常，context={}, error={}", 
            JSON.toJSONString(context), e.getMessage(), e);
        throw new BusinessException("", "用户信息查询异常");
    }
}

/**
 * @description: 外部响应对象转换为内部DTO
 * @param response 外部响应对象
 * @return UserInfoDTO 内部DTO对象
 * <AUTHOR>
 * @date 2025-09-07 10:24:47
 * @since JDK 1.8
 */
private UserInfoDTO convertToUserInfoDTO(ExternalUserQueryResponse response) {
    UserInfoDTO dto = new UserInfoDTO();
    dto.setUserId(response.getUserId());
    dto.setUserName(response.getUserName());
    dto.setEmail(response.getEmail());
    dto.setStatus(response.getStatus());
    return dto;
}
```

## 7. 异常处理规范

### 7.1 异常处理原则

- 所有外部接口调用必须使用try-catch包装
- 异常必须转换为BusinessException向上抛出
- 必须记录详细的异常日志，包含请求参数和响应参数

### 7.2 异常处理模板

```java
try {
    // 外部接口调用
} catch (Exception e) {
    log.error("外部接口调用异常，request={}, error={}", 
        JSON.toJSONString(request), e.getMessage(), e);
    throw new BusinessException("", "接口调用失败");
}
```

## 8. 日志规范

### 8.1 日志级别使用

- **INFO**: 记录接口调用的关键信息，如请求参数和响应结果
- **ERROR**: 记录异常信息和失败场景
- **DEBUG**: 记录详细的调试信息（生产环境不开启）

### 8.2 日志内容规范

```java
// 请求日志
log.info("调用外部接口开始，method={}, request={}", 
    "methodName", JSON.toJSONString(request));

// 响应日志  
log.info("调用外部接口结束，method={}, response={}", 
    "methodName", JSON.toJSONString(response));

// 异常日志
log.error("调用外部接口异常，method={}, request={}, error={}", 
    "methodName", JSON.toJSONString(request), e.getMessage(), e);
```

## 9. 代码风格规范

### 9.1 注解使用

- 实体类优先使用 `@Setter`/`@Getter`而非 `@Data`
- 服务类使用 `@Service`注解
- 日志使用 `@Slf4j`注解

### 9.2 工具类使用

- 字符串判空使用 `org.apache.commons.lang3.StringUtils.isEmpty`
- 集合判空使用 `org.apache.commons.collections.CollectionUtils.isEmpty`
- 禁止使用 `BeanUtils.copyProperties`进行属性拷贝

### 9.3 常量定义

- 在封装类中定义相关的业务常量
- 常量使用全大写，单词间下划线分隔
- 添加清晰的注释说明

## 10. 性能优化规范

### 10.1 批量操作

- 优先使用批量接口替代循环单次调用
- 合理设置批量大小，避免单次请求过大

## 11. 示例完整实现

### 11.1 目录结构示例

```
com.howbuy.cs.outservice.auth
├── EncryptOuterService.java
└── domain
    ├── EncryptContext.java
    └── EncryptResultDTO.java
```

### 11.2 完整代码示例

参考现有的 `SendMessageOuterService`实现，按照本规范进行重构和优化。

## 12. 检查清单

在开发外部Dubbo接口封装时，请按照以下清单进行自检：

- [ ] 包结构是否符合规范
- [ ] 类命名是否符合规范
- [ ] 入参是否正确封装（>4个参数使用Context）
- [ ] 返回值是否正确封装（>1个字段使用DTO）
- [ ] 异常处理是否完整
- [ ] 日志记录是否符合规范
- [ ] 代码风格是否符合项目要求
- [ ] 注释是否完整规范
- [ ] 单元测试是否编写

遵循本规范可确保外部Dubbo接口封装的一致性和可维护性。
