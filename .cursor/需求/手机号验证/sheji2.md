## 1. 数据库设计 (Oracle)

根据概要设计，我们在CRM业务库中创建一张任务表，用于存储待处理和已处理的手机号信息。

### 1.1. DDL/DML 脚本

```sql
-- 创建表
CREATE TABLE CM_PHONE_VALIDATION (
    ID NUMBER(20) NOT NULL,
    CONS_CUST_NO VARCHAR2(32) NOT NULL,
    MOBILE_ENCRYPTED VARCHAR2(256),
    CHECK_RESULT NUMBER(2),
    PROCESS_FLAG NUMBER(2) NOT NULL,
    PROCESS_REMARK VARCHAR2(256),
    AREA VARCHAR2(64),
    OPERATOR_TYPE VARCHAR2(32),
    CREATE_TIME DATE DEFAULT SYSDATE NOT NULL,
    UPDATE_TIME DATE DEFAULT SYSDATE NOT NULL,
    CONSTRAINT PK_CM_PHONE_VALIDATION PRIMARY KEY (ID)
);

-- 创建表注释
COMMENT ON TABLE CM_PHONE_VALIDATION IS '手机号可用性校验任务表';

-- 创建列注释
COMMENT ON COLUMN CM_PHONE_VALIDATION.ID IS '唯一主键，通过序列生成';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CONS_CUST_NO IS '投顾客户号';
COMMENT ON COLUMN CM_PHONE_VALIDATION.MOBILE_ENCRYPTED IS '加密的手机号';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CHECK_RESULT IS '校验结果 (0:空号, 1:实号, 2:停机, 3:库无, 4:沉默号, 5:风险号)';
COMMENT ON COLUMN CM_PHONE_VALIDATION.PROCESS_FLAG IS '处理标记 (0:未处理, 1:处理中, 2:处理完成, 3:处理失败)';
COMMENT ON COLUMN CM_PHONE_VALIDATION.PROCESS_REMARK IS '处理失败时的备注信息';
COMMENT ON COLUMN CM_PHONE_VALIDATION.AREA IS '手机号归属地';
COMMENT ON COLUMN CM_PHONE_VALIDATION.OPERATOR_TYPE IS '运营商类型';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CM_PHONE_VALIDATION.UPDATE_TIME IS '最后更新时间';
-- 创建索引，用于加速查询待处理任务
CREATE INDEX IDX_PHONE_VALIDATION_FLAG ON CM_PHONE_VALIDATION (PROCESS_FLAG);
/
```

## 2. 系统详细设计

### 2.1. cs-task 系统

作为流程编排者，`cs-task` 将新增一个定时任务来处理手机号校验。

#### 2.1.1. 定时任务实现规范

根据项目现有架构，定时任务采用基于消息队列的调度机制：

* **继承架构**: 手机号校验定时任务需继承 `AbstractBatchMessageJob` 抽象类，该类提供了完整的消息处理框架，包含分布式锁控制、日志记录、异常处理等功能。
* **消息队列触发**: 任务通过消息队列 `TOPIC_PHONE_VALIDATION` 触发，在配置文件中定义队列名称，支持线上动态调整。
* **分布式锁控制**: `AbstractBatchMessageJob` 内置分布式锁机制，确保多实例部署时同一任务不会重复执行，锁超时时间默认600秒，可通过重写 `getExpireSecond()` 方法自定义。

#### 2.1.2. 核心业务处理逻辑

* 创建一个线程池，线程池最大数量为5
* 创建一个循环执行下面逻辑
  * 查询CM_PHONE_VALIDATION表中记录标记PROCESS_FLAG=0-未处理的记录列表phoneList，并按照id进行排序，每次查询1000条记录
  * 判断phoneList是否为空，如果为空，则跳出循环
  * 对phoneList进行拆分，每组50条记录
  * 对分组列表进行遍历，下面每个分组处理都是提交到线程池中进行处理
    * 调用outerservice中封装好的批量手机号解密接口，对手机号进行解密，得到解密后的手机号列表
    * 根据id列表批量将本次要处理的记录处理标记（PROCESS_FLAG）更新为1-处理中
    * 调用outerservice中封装好的批量手机号验证接口，对手机号进行验证，得到验证结果列表
    * 将验证结果中的校验结果、手机号归属地、运营上类型、处理失败时的备注信息、最后更新时间=当前系统时间 设置到对应的数据库对象中
    * 批量将更新结果提交到数据库，注意：如果批量更新失败，要改成单条更新，保证更新成功率
  * 继续循环执行
* 全部任务处理完成销毁线程池
