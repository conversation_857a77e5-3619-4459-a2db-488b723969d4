## 3.外部接口

### 3.1.批量手机号解密接口apidoc

接口：com.howbuy.auth.facade.decrypt.DecryptBatchFacade

协议：dubbo

入参：加密信息列表List

出参类：com.howbuy.auth.facade.response.CodecBatchResponse

```
//明文映射集合，key：密文，value：明文
Map<String, String> codecMap
String retCode = "0000000";// 0000000-成功
String retDesc;
```

### 3.2.批量验证手机号接口apidoc

引入接口包依赖，maven

```
<artifactId>crm-export-client</artifactId>
<groupId>com.howbuy.crm</groupId>
<version>********-RELEASE</version>
```

接口文档

```


/**
     * @api {DUBBO} com.howbuy.crm.export.client.facade.mobile.BatchMobileCheckFacade.execute(request) execute()
     * @apiVersion 1.0.0
     * @apiGroup BatchMobileCheckFacadeService
     * @apiName 批量验证手机号
     * @apiDescription 批量验证手机号状态，检测手机号是否为空号、实号等状态信息
     * @apiParam (请求参数) {List<String>} mobileList 手机号列表
     * @apiParam (请求参数) {Integer} [type] 查询类型 1：MD5(32位小写)，0：普通手机号；默认0
     * @apiParamExample 请求参数示例
     * {
     *   "mobileList": ["13800138000", "13900139000"],
     *   "type": 0
     * }
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {List<Object>} data.checkResults 验证结果列表
     * @apiSuccess (响应结果) {String} data.checkResults.mobile 手机号
     * @apiSuccess (响应结果) {String} data.checkResults.area 手机号所属区域
     * @apiSuccess (响应结果) {String} data.checkResults.numberType 手机号运营商类型
     * @apiSuccess (响应结果) {Integer} data.checkResults.status 检测结果，枚举值： 0：空号 1：实号 2：停机 3：库无 4：沉默号 5：风险号
     * @apiSuccess (响应结果) {String} data.checkResults.statusDesc 检测结果描述
     * @apiSuccess (响应结果) {String} data.checkResults.lastTime 时间戳，毫秒
     * @apiSuccess (响应结果) {Integer} data.totalCount 总数量
     * @apiSuccess (响应结果) {Integer} data.successCount 成功数量
     * @apiSuccess (响应结果) {Integer} data.failCount 失败数量
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "SUCCESS",
     *   "description": "成功",
     *   "data": {
     *     "checkResults": [
     *       {
     *         "mobile": "13800138000",
     *         "area": "北京-北京",
     *         "numberType": "中国移动",
     *         "status": 1,
     *         "statusDesc": "实号",
     *         "lastTime": "1522080000000"
     *       }
     *     ],
     *     "totalCount": 2,
     *     "successCount": 1,
     *     "failCount": 1
     *   }
     * }
     */
```
