# 手机号可用性校验服务 - 详细设计文档

> **文档目的**：本详细设计文档（DDD）基于PRD和概要设计，为开发人员提供可实施的技术方案、接口定义和处理流程。

---

## 1. 修订历史

| 版本 | 日期       | 作者        | 修订内容                                     |
| :--- | :--------- | :---------- | :------------------------------------------- |
| 1.0  | 2025-09-01 | Gemini      | 初稿创建                                     |
| 1.1  | 2025-09-02 | hongdong.xie | 根据项目实际规范优化定时任务实现方案         |

---

## 2. 总体设计回顾

本服务旨在通过定时任务，自动化地校验一批存量客户手机号的可用性。涉及以下三个核心系统：

1. **cs-task (定时任务处理系统)**: 任务的调度方和总控制器，负责编排整个校验流程。
2. **howbuy-auth (加解密系统)**: 提供手机号密文的解密服务。
3. **crm-export (外部接口调用网关)**: 封装对第三方 `AISpace` 服务的调用，并提供统一的内部接口。

### 2.1. 业务流程图

```plantuml
@startuml
!theme plain
autonumber "<b>[00]"
title 手机号可用性校验服务 - 业务流程图（乐观锁+并行处理）

actor "定时任务" as scheduler

box "我方系统" #LightYellow
    participant "cs-task\n(定时任务处理系统)" as task
    participant "AuthOuterService\n(解密服务封装)" as auth
    participant "CrmExportOuterService\n(校验服务封装)" as export
    participant "线程池\n(5个线程)" as pool
    database "Oracle数据库\n(CM_PHONE_VALIDATION)" as db
end box

box "外部服务" #LightGreen
    participant "howbuy-auth\n(解密服务)" as authService
    participant "crm-export\n(校验网关)" as exportService
    participant "AISpace API\n(手机号状态校验)" as aispace
end box

scheduler -> task: 触发执行
activate task

loop 分页处理循环
    task -> db: 1. 分页查询待处理手机号\n(PROCESS_FLAG=0, 普通查询)
    activate db
    db --> task: 2. 返回手机号列表(含VERSION)
    deactivate db

    loop 每条记录乐观锁更新
        task -> db: 3. 乐观锁更新状态\n(PROCESS_FLAG=1, VERSION+1)
        activate db
        alt 更新成功(VERSION匹配)
            db --> task: 4a. 更新成功，获得处理权
        else 更新失败(VERSION不匹配)
            db --> task: 4b. 更新失败，已被其他实例处理
        end
        deactivate db
    end

    task -> pool: 5. 将成功获锁的记录分组提交线程池
    activate pool

    par 并行处理(5个线程)
        pool -> auth: 6. 批量解密手机号
        activate auth
        auth -> authService: 7. Dubbo调用解密服务
        activate authService
        authService --> auth: 8. 返回解密结果
        deactivate authService
        auth --> pool: 9. 返回解密后明文
        deactivate auth

        loop 子批次处理(每批次<=100个)
            pool -> export: 10. 批量校验手机号
            activate export
            export -> exportService: 11. Dubbo调用校验网关
            activate exportService
            exportService -> aispace: 12. HTTP调用AISpace接口
            activate aispace
            aispace --> exportService: 13. 返回校验结果
            deactivate aispace
            exportService --> export: 14. 返回校验结果
            deactivate exportService
            export --> pool: 15. 返回校验结果
            deactivate export
        end
    end

    pool --> task: 16. 所有线程处理完成
    deactivate pool

    task -> db: 17. 批量更新处理结果\n(CHECK_RESULT, AREA, PROCESS_FLAG=2或3)
    activate db
    db --> task: 18. 确认更新
    deactivate db
end

deactivate task
@enduml
```

---

## 3. 数据库设计 (Oracle)

根据概要设计，我们在CRM业务库中创建一张任务表，用于存储待处理和已处理的手机号信息。

### 3.1. 表结构定义

* **表名**: `CM_PHONE_VALIDATION`
* **表注释**: 手机号可用性校验任务表

| 字段名               | 数据类型          | 可否为空 | 主键 | 注释                                                          |
| :------------------- | :---------------- | :------- | :--- | :------------------------------------------------------------ |
| `ID`               | `NUMBER(20)`    | 否       | 是   | 唯一主键，通过序列生成                                        |
| `CONS_CUST_NO`     | `VARCHAR2(32)`  | 否       |      | 投顾客户号                                                    |
| `MOBILE_ENCRYPTED` | `VARCHAR2(256)` | 是       |      | 加密的手机号                                                  |
| `CHECK_RESULT`     | `NUMBER(2)`     | 是       |      | 校验结果 (0:空号, 1:实号, 2:停机, 3:库无, 4:沉默号, 5:风险号) |
| `PROCESS_FLAG`     | `NUMBER(2)`     | 否       |      | 处理标记 (0:未处理, 1:处理中, 2:处理完成, 3:处理失败)         |
| `PROCESS_REMARK`   | `VARCHAR2(256)` | 是       |      | 处理失败时的备注信息，如：解密异常、校验超时                  |
| `AREA`             | `VARCHAR2(64)`  | 是       |      | 手机号归属地，如：上海-上海                                   |
| `OPERATOR_TYPE`    | `VARCHAR2(32)`  | 是       |      | 运营商类型，如：中国移动                                      |
| `CREATE_TIME`      | `DATE`          | 否       |      | 记录创建时间，默认为 `SYSDATE`                              |
| `UPDATE_TIME`      | `DATE`          | 否       |      | 记录最后更新时间，默认为 `SYSDATE`                          |
| `VERSION`          | `NUMBER(10)`    | 否       |      | 乐观锁版本号，用于并发控制，默认为1                           |

### 3.2. DDL/DML 脚本

```sql
-- 创建表
CREATE TABLE CM_PHONE_VALIDATION (
    ID NUMBER(20) NOT NULL,
    CONS_CUST_NO VARCHAR2(32) NOT NULL,
    MOBILE_ENCRYPTED VARCHAR2(256),
    CHECK_RESULT NUMBER(2),
    PROCESS_FLAG NUMBER(2) NOT NULL,
    PROCESS_REMARK VARCHAR2(256),
    AREA VARCHAR2(64),
    OPERATOR_TYPE VARCHAR2(32),
    CREATE_TIME DATE DEFAULT SYSDATE NOT NULL,
    UPDATE_TIME DATE DEFAULT SYSDATE NOT NULL,
    VERSION NUMBER(10) DEFAULT 1 NOT NULL,
    CONSTRAINT PK_CM_PHONE_VALIDATION PRIMARY KEY (ID)
);

-- 创建表注释
COMMENT ON TABLE CM_PHONE_VALIDATION IS '手机号可用性校验任务表';

-- 创建列注释
COMMENT ON COLUMN CM_PHONE_VALIDATION.ID IS '唯一主键，通过序列生成';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CONS_CUST_NO IS '投顾客户号';
COMMENT ON COLUMN CM_PHONE_VALIDATION.MOBILE_ENCRYPTED IS '加密的手机号';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CHECK_RESULT IS '校验结果 (0:空号, 1:实号, 2:停机, 3:库无, 4:沉默号, 5:风险号)';
COMMENT ON COLUMN CM_PHONE_VALIDATION.PROCESS_FLAG IS '处理标记 (0:未处理, 1:处理中, 2:处理完成, 3:处理失败)';
COMMENT ON COLUMN CM_PHONE_VALIDATION.PROCESS_REMARK IS '处理失败时的备注信息';
COMMENT ON COLUMN CM_PHONE_VALIDATION.AREA IS '手机号归属地';
COMMENT ON COLUMN CM_PHONE_VALIDATION.OPERATOR_TYPE IS '运营商类型';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN CM_PHONE_VALIDATION.UPDATE_TIME IS '记录最后更新时间';
COMMENT ON COLUMN CM_PHONE_VALIDATION.VERSION IS '乐观锁版本号';

-- 创建索引，用于加速查询待处理任务
CREATE INDEX IDX_PHONE_VALIDATION_FLAG ON CM_PHONE_VALIDATION (PROCESS_FLAG);
/
```

---

## 4. 系统详细设计

### 4.1. cs-task 系统

作为流程编排者，`cs-task` 将新增一个定时任务来处理手机号校验。

#### 4.1.1. 定时任务实现规范

根据项目现有架构，定时任务采用基于消息队列的调度机制：

* **继承架构**: 手机号校验定时任务需继承 `AbstractBatchMessageJob` 抽象类，该类提供了完整的消息处理框架，包含分布式锁控制、日志记录、异常处理等功能。
* **消息队列触发**: 任务通过消息队列 `TOPIC_PHONE_VALIDATION` 触发，在配置文件中定义队列名称，支持线上动态调整。
* **分布式锁控制**: `AbstractBatchMessageJob` 内置分布式锁机制，确保多实例部署时同一任务不会重复执行，锁超时时间默认600秒，可通过重写 `getExpireSecond()` 方法自定义。
* **执行结果反馈**: 任务执行结果自动上报给调度中心，支持调度平台统一管理和监控。
* **异常处理**: 框架提供统一的异常捕获和处理机制，执行异常时会自动记录日志并反馈失败状态。

#### 4.1.2. 核心业务处理逻辑

这是 `PhoneValidationService` 中 `processPendingTasks` 方法需要实现的详细业务步骤，结合分页查询和线程池并行处理以提升效率。

1. **任务启动与初始化**: 
   * 记录任务开始执行的日志，便于问题排查。
   * 初始化线程池（大小为5个线程）用于并行处理数据。
   
2. **分页循环处理**: 启动分页循环，持续处理数据直到所有待处理项都完成：

3. **(分页循环内) 分页查询待处理任务**:
   * 通过 Mapper 调用分页查询：`SELECT * FROM CM_PHONE_VALIDATION WHERE PROCESS_FLAG = 0 ORDER BY ID LIMIT :pageSize OFFSET :offset`。
   * `:pageSize` 为分页大小（建议值1000），`:offset` 为偏移量，两个参数都可在配置文件中配置。
   * **退出条件**: 如果本次查询返回的列表为空，说明已无待处理数据，优雅地跳出分页循环。

4. **(分页循环内) 乐观锁更新状态**:
   * 获取到数据后，使用乐观锁机制批量更新任务状态：
   * 执行SQL：`UPDATE CM_PHONE_VALIDATION SET PROCESS_FLAG = 1, VERSION = VERSION + 1 WHERE ID = ? AND VERSION = ?`
   * 只有VERSION匹配的记录才会被更新，防止并发冲突。更新失败的记录说明已被其他实例处理，直接跳过。

5. **(分页循环内) 数据分组并行处理**:
   * 将当前页的数据按照线程池大小（5个线程）进行分组，每组数据提交给一个线程处理。
   * 每个线程内部执行以下逻辑：

     a. **数据准备与分组**:
        * 创建一个 `Map<String, PhoneValidationTask>`，用于后续根据手机号密文快速反向查找到原始任务对象。
        * 遍历分组的任务列表：
          * 如果 `MOBILE_ENCRYPTED` 字段为空或无效，直接标记为失败（`PROCESS_FLAG=3`），`PROCESS_REMARK` 记为"手机号密文不存在"。
          * 如果密文有效，则将其加入待解密列表，并填充Map。

     b. **批量解密**:
        * 如果待解密列表不为空，则通过 OuterService 调用 `authOuterService.batchDecryptPhones(encryptedPhones)`。
        * 遍历解密返回的结果。对于解密失败的，标记任务为失败（`PROCESS_FLAG=3`），`PROCESS_REMARK` 记为"解密服务调用失败"。

     c. **批量校验**:
        * 由于 `AISpace` 接口限制单次最多100个手机号，将成功解密的明文手机号列表切分为大小不超过100的子批次。
        * 遍历子批次，通过 OuterService 调用 `crmExportOuterService.batchValidatePhones(subList)`。
        * **异常处理**: 对 OuterService 调用进行异常处理。如果接口调用失败，则该子批次中的所有手机号任务都标记为失败（`PROCESS_FLAG=3`），`PROCESS_REMARK` 记为"校验服务调用异常"。

     d. **结果聚合**:
        * 遍历校验服务成功返回的结果列表。
        * 根据返回的手机号明文，找到对应的原始任务对象。
        * 将 `CHECK_RESULT`, `AREA`, `OPERATOR_TYPE` 等信息填充到任务对象中，并将 `PROCESS_FLAG` 设为 `2` (处理完成)。

6. **(分页循环内) 等待线程池完成**:
   * 等待当前页所有线程处理完成后，再进行下一页的处理。

7. **(分页循环内) 批量更新处理结果**:
   * 将当前页所有已成功获得乐观锁的任务的处理结果（包括校验结果、归属地、运营商等信息），通过MyBatis的 `updateTaskResultBatch` 方法一次性更新回数据库。

8. **任务结束**: 
   * 关闭线程池，等待所有任务完成。
   * 记录任务执行完毕的日志，包括总共处理的页数、成功总数和失败总数。

#### 4.1.3. 主要类和方法设计

按照项目规范，定时任务的实现架构如下：

```java
// package com.howbuy.cs.job;
@Slf4j
@Component
public class PhoneValidationJob extends AbstractBatchMessageJob {

    @Value("${sync.TOPIC_PHONE_VALIDATION}")
    private String queue;

    @Autowired
    private PhoneValidationService phoneValidationService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("手机号可用性校验定时任务开始执行...");
        try {
            phoneValidationService.processPendingTasks(getContent(message));
        } catch (Exception e) {
            log.error("手机号可用性校验定时任务执行异常", e);
            throw e; // 重新抛出异常，让框架处理
        }
        log.info("手机号可用性校验定时任务执行完成");
    }
}

// package com.howbuy.cs.task.service;
@Service
public class PhoneValidationService {
    @Autowired
    private PhoneValidationTaskMapper taskMapper;

    // 注入OuterService封装层，不直接调用外部Dubbo接口
    @Autowired
    private AuthOuterService authOuterService;

    @Autowired
    private CrmExportOuterService crmExportOuterService;

    public void processPendingTasks(String messageContent) {
        // ... 实现 4.1.2 中的核心逻辑流程 ...
    }
}

// package com.howbuy.cs.task.mapper;
@Mapper
public interface PhoneValidationTaskMapper {
    /**
     * 分页查询待处理任务
     */
    List<PhoneValidationTask> selectPendingTasksByPage(@Param("pageSize") int pageSize, @Param("offset") int offset);
    
    /**
     * 乐观锁更新单个任务状态（返回影响行数，用于判断是否更新成功）
     */
    int updateTaskStatusWithVersion(@Param("id") Long id, @Param("processFlag") Integer processFlag, 
                                   @Param("version") Integer version, @Param("processRemark") String processRemark);
    
    /**
     * 批量更新任务结果（已获得锁的任务）
     */
    int updateTaskResultBatch(List<PhoneValidationTask> tasks);
}
```

### 4.2. crm-export 系统

作为网关，`crm-export` 负责封装对 `AISpace` 的调用，通过 Dubbo 协议对外提供服务。

#### 4.2.1. Dubbo 接口定义

`crm-export` 需要暴露以下 Dubbo 服务接口：

```java
// package com.howbuy.crm.export.service;
public interface PhoneValidationService {
    /**
     * 批量校验手机号可用性
     * @param phoneList 手机号明文列表
     * @return 校验结果
     */
    BatchValidationResponse batchValidatePhones(List<String> phoneList);
}

// Response DTO:
@Setter
@Getter
public class BatchValidationResponse {
    private Integer chargeStatus;
    private String chargeCount;
    private String message;
    private String code;
    private List<ValidationData> data;
}

@Setter
@Getter
public class ValidationData {
    private String mobile;
    private String lastTime;
    private String area;
    private String numberType;
    private String status; // 0:空号, 1:实号...
}
```

#### 4.2.2. OuterService 封装层

按照项目规范，外部 Dubbo 接口调用需要在 OuterService 中封装：

```java
// package com.howbuy.cs.outerservice;
@Service
public class CrmExportOuterService {
    
    @DubboReference(version = "1.0.0", group = "export", check = false)
    private PhoneValidationService phoneValidationService;
    
    /**
     * 批量校验手机号可用性
     * @param phoneList 手机号明文列表
     * @return 校验结果
     */
    public BatchValidationResponse batchValidatePhones(List<String> phoneList) {
        try {
            return phoneValidationService.batchValidatePhones(phoneList);
        } catch (Exception e) {
            log.error("调用crm-export手机号校验服务异常", e);
            throw new RuntimeException("手机号校验服务调用失败", e);
        }
    }
}
```

#### 4.2.3. 核心逻辑

1. **接收请求**: 接收 `List<String>` 格式的手机号明文。
2. **配置读取**: 从项目配置中心（bootstrap.properties 或 Nacos）获取 `AISpace` 的 `X-APISpace-Token`。
3. **构建HTTP请求**:
   * 使用项目标准的 `RestTemplate` 或 `OkHttpClient`。
   * 将手机号列表拼接成 `mobiles=138...,139...` 的格式。
   * 设置 `Content-Type` 为 `application/x-www-form-urlencoded`。
   * 添加 `X-APISpace-Token` 到 Header。
4. **调用与重试**:
   * 遵循项目异常处理规范，使用 `spring-retry` 实现重试机制。
   * `@Retryable(value = {IOException.class}, maxAttempts = 3, backoff = @Backoff(delay = 2000))`
   * 设置合理的连接和读取超时时间（建议5-10秒，避免频繁超时）。
5. **异常处理与日志**: 
   * 使用 `AlertLogUtil.alert()` 进行关键异常告警，遵循项目告警规范。
   * 记录详细的调用日志，便于问题排查。
6. **解析与返回**: 解析 `AISpace` 返回的JSON，并将其转换为 `BatchValidationResponse` 对象返回。

### 4.3. howbuy-auth 系统 (Dubbo 接口)

`howbuy-auth` 通过 Dubbo RPC 提供解密服务。`cs-task` 系统将通过 Dubbo 客户端调用此服务。

#### 4.3.1. 实际 Dubbo 接口

`howbuy-auth` 系统已提供批量解密接口：

```java
// package com.howbuy.auth.facade.decrypt;
public interface DecryptBatchFacade {
    /**
     * 批量解密数据
     * @param encryptedData 待解密的密文列表
     * @return 返回解密结果，Key为原始密文，Value为解密后的明文
     */
    Map<String, String> decryptBatch(List<String> encryptedData);
}
```

#### 4.3.2. OuterService 封装层

按照项目规范，外部 Dubbo 接口需要在 OuterService 中进行封装，不能在业务代码中直接调用：

```java
// package com.howbuy.cs.outerservice;
@Service
public class AuthOuterService {
    
    @DubboReference(version = "2.0.1-RELEASE", group = "auth", check = false)
    private DecryptBatchFacade decryptBatchFacade;
    
    /**
     * 批量解密手机号
     * @param encryptedPhones 加密手机号列表
     * @return 解密结果 Map<密文, 明文>
     */
    public Map<String, String> batchDecryptPhones(List<String> encryptedPhones) {
        try {
            return decryptBatchFacade.decryptBatch(encryptedPhones);
        } catch (Exception e) {
            log.error("调用howbuy-auth解密服务异常", e);
            throw new RuntimeException("解密服务调用失败", e);
        }
    }
}
```

---

## 5. 异常处理设计

| 异常场景                            | 处理系统       | 处理逻辑                                                                                          | 日志级别  | 最终 `PROCESS_FLAG` | `PROCESS_REMARK` 示例   | 告警处理 |
| :---------------------------------- | :------------- | :------------------------------------------------------------------------------------------------ | :-------- | :-------------------- | :------------------------ | :-------- |
| 数据库中无手机号密文                | `cs-task`    | 任务获取后发现 `MOBILE_ENCRYPTED` 为空。                                                        | `WARN`  | `3` (失败)          | `手机号密文不存在`      | 无 |
| `howbuy-auth` 解密服务失败        | `cs-task`    | Dubbo调用失败或返回错误。记录日志，将该手机号标记为失败。                                         | `ERROR` | `3` (失败)          | `解密服务调用失败`      | 使用AlertLogUtil告警 |
| `crm-export` 校验服务失败         | `cs-task`    | Dubbo调用失败（含重试后）。记录日志，将该批次手机号标记为失败。                                   | `ERROR` | `3` (失败)          | `校验服务调用失败`      | 使用AlertLogUtil告警 |
| `AISpace` 接口返回业务错误        | `crm-export` | `AISpace` 返回非 `200000` 的 code。`crm-export` 应直接透传此错误信息。                      | `WARN`  | `3` (失败)          | `AISpace: 账号余额不足` | 记录日志 |
| `AISpace` 返回 `UNKNOWN` (库无) | `cs-task`    | `CHECK_RESULT` 记为 `3`，`PROCESS_FLAG` 记为 `2` (处理完成)，因为这是一个明确的返回结果。 | `INFO`  | `2` (完成)          | -                         | 无 |
| 分布式锁获取失败                   | `cs-task`    | `AbstractBatchMessageJob` 框架处理，记录日志后直接返回，不执行业务逻辑。                     | `INFO`  | - | -                         | 无 |

---

## 6. 非功能性需求实现

* **性能**:
  * **分页批量处理**: 采用分页查询机制，`pageSize` 通过配置文件可配置（建议1000），避免单次查询数据量过大。
  * **线程池并行处理**: 使用固定大小的线程池（5个线程）对每页数据进行并行处理，显著提升处理效率。
  * **乐观锁并发控制**: 使用乐观锁版本号机制替代悲观锁，避免长时间锁表，提升并发性能，支持多实例安全并行处理。
  * **分布式处理**: 基于 `AbstractBatchMessageJob` 的分布式锁机制，配合数据库乐观锁，双重保障避免重复处理。
* **安全性**:
  * **禁止明文日志**: 严格遵守项目安全规范，在任何系统的日志中都不得出现手机号明文。关联问题使用 `CONS_CUST_NO`。
  * **配置安全**: `X-APISpace-Token` 等敏感配置存储在项目配置中心，运行时动态获取。
* **可维护性**:
  * **规范化日志**: 遵循项目日志规范，任务开始/结束时打印包含处理总数、成功数、失败数的摘要日志。
  * **失败追踪**: 每个失败记录都在 `PROCESS_REMARK` 字段中记录明确原因，便于问题排查。
  * **告警机制**: 遵循项目告警规范，使用 `AlertLogUtil.alert()` 进行关键异常告警。任务结束时计算失败率，如果超过阈值则主动告警。
* **可靠性**:
  * **幂等性保证**: `PROCESS_FLAG` 状态机制确保任务重复执行时不会重复处理数据。
  * **框架级重试**: 利用 `AbstractBatchMessageJob` 框架的异常处理机制，配合 `spring-retry` 实现可靠的重试策略。
  * **事务控制**: 关键数据库操作使用事务控制，确保数据一致性。

---

## 7. 待定问题与决策

* **Q1: AISpace接口的QPS限制和调用频率控制？**
  * **决策**: 在 `crm-export` 的 `AISpace` 调用逻辑中，增加可配置的限流控制（可使用 Guava RateLimiter 或项目现有限流组件），并配置保守的初始QPS限制。在 `cs-task` 的批次循环间增加可配置的延时机制。
* **Q2: 数据源写入方式？**
  * **决策**: 假设为增量插入模式。设计中的 `PROCESS_FLAG = 0` 查询方式天然支持增量处理，符合项目数据处理规范。
* **Q3: 对于AISpace返回 `UNKNOWN` (库无) 状态如何处理？**
  * **决策**: 视为有效校验结果。`CHECK_RESULT` 记为 `3` (库无)，`PROCESS_FLAG` 记为 `2` (处理完成)，不需重试。
* **Q4: 定时任务调度方式？**  
  * **决策**: 采用项目现有的基于消息队列的调度架构，通过 `AbstractBatchMessageJob` 框架统一管理，确保与现有定时任务的一致性和可维护性。
* **Q5: 配置参数管理？**
  * **决策**: 所有可调整参数（批次大小、重试次数、超时时间等）通过项目配置文件或配置中心管理，支持线上动态调整，无需重启应用。
