# ✅ CS-Task-Server定时任务代码生成提示词（基于详细设计文档）

你是一名资深后端开发工程师，请根据我提供的**定时任务详细设计文档**内容，生成符合CS-Task-Server项目标准的**定时任务代码实现**，输出要求如下：

## 📌 输出要求：

- **输出格式**：Java 源代码，分模块输出，可直接粘贴到项目中使用
- **遵循规范**：必须严格遵循项目规范，规则文件路径为 `.cursor/rules/`
- **避免重复造轮子**：请优先使用项目中已有的工具类、封装模块、基础框架，不得重复实现已有功能
- **定时任务架构**：基于消息队列触发的分布式定时任务框架，支持分布式锁和任务状态反馈
- **清晰注释**：对关键逻辑、判断分支、批处理逻辑、异常处理进行清晰注释
- **可维护性**：结构清晰、职责单一、易测试易扩展，体现生产级代码风格

## 📘 输入内容包含：

由我提供的**定时任务详细设计文档**，结构如下：

- **任务名称**：定时任务的功能名称
- **执行频率**：任务执行的时间规则（如每日、每小时等）
- **触发方式**：通过消息队列触发的Queue名称配置
- **任务参数**：任务执行所需的输入参数
- **业务处理流程**：包括数据查询、业务逻辑、批处理策略、异常策略
- **调用依赖模块**：例如DAO、Service、外部接口OutService等
- **异常处理策略**：包括告警机制、日志记录、失败处理等
- **批处理策略**：是否需要分页处理大数据量

## 🧠 你的实现任务：

### 1. **定时任务主类（Job层）**

生成继承自 `AbstractBatchMessageJob` 的定时任务类，包含：

- 标准版权头注释（Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.）
- 使用 `@Service` 注解注册到Spring容器
- 通过 `@Value("${sync.QUEUE_CONFIG_NAME}")` 获取队列配置
- 实现 `getQuartMessageChannel()` 方法返回队列名称
- 实现 `doProcessMessage(SimpleMessage message)` 方法处理具体业务逻辑
- 注入对应的业务处理类（Buss或Service）
- 标准的异常处理和告警机制

### 2. **业务处理类（Buss层）**

生成专门的业务处理类，包含：

- 标准版权头注释
- 使用 `@Service("businessNameTaskBuss")` 注解并指定bean名称
- 注入所需的DAO、Service、OutService等依赖
- 实现具体的业务处理逻辑
- 合理的日志记录和性能监控
- 如需批处理，使用分页查询和批量操作
- 完善的异常处理机制

### 3. **数据访问层（可选）**

根据需要生成或完善相关的：

- DAO/Mapper接口方法
- SQL映射配置
- Model实体类

### 4. **外部服务封装（可选）**

根据需要生成外部服务调用封装：

- OutService类
- Context参数类
- DTO返回对象类

### 5. **配置参数**

生成相关的配置参数定义：

- 队列名称配置
- 批处理大小配置
- 其他业务相关配置

## 📋 CS-Task-Server定时任务架构规范：

### 基础架构特点

```java
// 1. 定时任务类继承AbstractBatchMessageJob
@Slf4j
@Service
public class ExampleTaskJob extends AbstractBatchMessageJob {
    
    // 2. 注入业务处理类
    @Autowired
    private ExampleTaskBuss exampleTaskBuss;
    
    // 3. 通过@Value获取队列配置
    @Value("${sync.EXAMPLE_TASK_QUEUE}")
    private String queue;
    
    // 4. 重写getQuartMessageChannel方法
    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }
    
    // 5. 实现doProcessMessage方法
    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("ExampleTaskJob process start");
        try {
            exampleTaskBuss.processExample();
        } catch (Exception e) {
            AlertLogUtil.alert("ExampleTaskJob", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
        }
        log.info("ExampleTaskJob process end");
    }
}
```

### 业务处理类规范

```java
// 业务处理类
@Slf4j
@Service("exampleTaskBuss")
public class ExampleTaskBuss {
    
    @Autowired
    private ExampleService exampleService;
    
    @Autowired
    private ExampleMapper exampleMapper;
    
    /**
     * @description: 业务处理主方法
     * @return void
     * @author: hongdong.xie
     * @date: 2025-09-07 16:16:00
     * @since JDK 1.8
     */
    public void processExample() {
        log.info("ExampleTaskBuss.processExample 开始执行");
        try {
            // 具体业务逻辑
        } catch (Exception e) {
            log.error("ExampleTaskBuss.processExample 执行异常", e);
            throw e;
        }
        log.info("ExampleTaskBuss.processExample 执行完成");
    }
}
```

### 标准版权头

```java
/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
```

### 方法注释规范

```java
/**
 * @description: 方法功能描述
 * @param paramName 参数说明
 * @return 返回值说明
 * @author: hongdong.xie
 * @date: 2025-09-07 16:16:00
 * @since JDK 1.8
 */
```

### 异常处理规范

```java
try {
    // 业务处理逻辑
    log.info("任务处理开始");
    // 具体逻辑...
    log.info("任务处理结束");
} catch (Exception e) {
    // 异常告警 - 使用AlertLogUtil
    AlertLogUtil.alert(this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
    // 记录异常日志
    log.error(e.getMessage(), e);
    // 重新抛出异常，让框架处理
    throw e;
}
```

### 批处理处理规范

```java
// 分页查询大数据量处理
int pageSize = 1000;
int pageNum = 1;
List<DataType> dataList;

do {
    PageHelper.startPage(pageNum, pageSize);
    dataList = exampleMapper.selectByCondition(condition);
    
    if (CollectionUtils.isNotEmpty(dataList)) {
        // 批量处理当前页数据
        processBatch(dataList);
        log.info("处理第{}页数据，数据量：{}", pageNum, dataList.size());
    }
    
    pageNum++;
} while (CollectionUtils.isNotEmpty(dataList) && dataList.size() == pageSize);
```

### 外部服务调用规范

```java
// 使用OutService封装外部调用
@Autowired
private CrmCustomerOuterService crmCustomerOuterService;

// 构建查询上下文
CustomerQueryContext context = new CustomerQueryContext();
context.setUserId(userId);
context.setStartDate(startDate);

// 调用外部服务
CustomerInfoDTO customerInfo = crmCustomerOuterService.queryCustomerInfo(context);
```

## 📂 文件存放位置规范：

### 定时任务类存放位置

- **Job类**：`cs-task-server/src/main/java/com/howbuy/cs/job/`
- **Buss类**：`cs-task-server/src/main/java/com/howbuy/cs/task/buss/`

### 其他相关类存放位置

- **Service类**：`cs-task-server/src/main/java/com/howbuy/cs/task/service/`
- **DAO接口**：`cs-task-server/src/main/java/com/howbuy/cs/task/dao/`
- **Mapper XML**：`cs-task-server/src/main/java/com/howbuy/cs/task/mapper/`
- **Model类**：`cs-task-server/src/main/java/com/howbuy/cs/task/model/`
- **OutService类**：`cs-task-server/src/main/java/com/howbuy/cs/outservice/{系统名}/`

### 命名规范

- **定时任务类**：以 `TaskJob` 结尾，如 `DataSyncTaskJob`、`CustomerProcessTaskJob`
- **业务处理类**：以 `TaskBuss` 结尾，如 `DataSyncTaskBuss`、`CustomerProcessTaskBuss`
- **外部服务类**：以 `OuterService` 结尾，如 `CrmCustomerOuterService`

## 🚫 禁止事项：

| 类型              | 说明                                                     |
| ----------------- | -------------------------------------------------------- |
| ❌ 重复造轮子     | 必须使用项目现有的 `AbstractBatchMessageJob` 框架      |
| ❌ 硬编码配置     | 队列名称必须通过 `@Value` 从配置文件获取               |
| ❌ 违反目录结构   | 所有类必须放在指定的包路径下                             |
| ❌ 缺少异常处理   | 必须使用 `AlertLogUtil` 进行异常告警                   |
| ❌ 不规范注释     | 必须使用标准的版权头和方法注释                           |
| ❌ 业务逻辑混乱   | Job类只处理消息，业务逻辑必须委托给Buss类               |
| ❌ 禁止使用魔法值 | 任何未经定义的字面量（字符串、数字）都应定义为常量或枚举 |
| ❌ 缺少枚举或常量 | 对于状态、类型等字段，必须创建对应的枚举类或常量         |
| ❌ 违反编码规范   | 字符串判空使用StringUtils.isEmpty，禁用BeanUtils.copyProperties |

## 🎯 自动支持的功能：

通过继承 `AbstractBatchMessageJob`，你的定时任务将自动获得：

- ✅ **分布式锁控制**：防止任务并发执行，使用Redis锁机制
- ✅ **任务状态反馈**：自动向调度中心反馈执行结果
- ✅ **统一异常处理**：框架级别的异常捕获和处理
- ✅ **执行时间统计**：自动记录任务执行耗时
- ✅ **日志链路追踪**：自动生成UUID进行日志追踪
- ✅ **消息处理封装**：统一的消息接收和处理机制

## 📋 项目特有工具和规范：

### 必须使用的工具类

- **AlertLogUtil**：统一告警工具，`AlertLogUtil.alert(className, message)`
- **StringUtils**：字符串工具，`StringUtils.isEmpty(str)`
- **DateTimeUtil**：日期时间工具类
- **Throwables**：异常堆栈工具，`Throwables.getStackTraceAsString(e)`

### 数据库操作规范

- 使用 **PageHelper** 进行分页查询
- 批量操作使用 **batchInsert**、**batchUpdate** 等方法
- 支持 **Oracle** 数据库特定语法

### 配置管理

- 使用 **Nacos** 配置中心
- 配置项命名：`sync.{BUSINESS_NAME}_QUEUE`
- 示例：`sync.GENERATE_CONSULTANT_CHANGEORG_REC`

### 外部服务集成

- 必须通过 **OutService** 层封装外部调用
- 使用 **Context** 类聚合入参（超过4个参数）
- 使用 **DTO** 类封装返回值（超过1个字段）

## 📎 提示词使用方式：

在我提供详细的定时任务设计文档后，请立即生成符合上述规范的完整Java实现代码，包括：

1. **定时任务主类**（继承AbstractBatchMessageJob）
2. **业务处理类**（包含核心业务逻辑）
3. **相关的DAO/Mapper方法**（如需要）
4. **外部服务封装类**（如需要）
5. **必要的配置参数定义**

确保代码结构清晰、注释完整、符合CS-Task-Server项目规范，可以直接在项目中使用。

## 💡 代码生成示例结构：

```
📁 生成的代码文件结构：
├── 📄 {BusinessName}TaskJob.java          (定时任务主类)
├── 📄 {BusinessName}TaskBuss.java         (业务处理类)  
├── 📄 {RelatedEntity}Mapper.java          (DAO接口，如需要)
├── 📄 {RelatedEntity}Mapper.xml           (Mapper映射，如需要)
├── 📄 {ExternalSystem}OuterService.java   (外部服务封装，如需要)
├── 📄 {Business}Context.java              (参数上下文，如需要)
├── 📄 {Business}DTO.java                  (返回数据传输对象，如需要)
└── 📄 application.properties配置项        (队列配置等)
```

请严格按照CS-Task-Server项目的实际架构和编码规范生成代码，确保生成的代码能够无缝集成到现有项目中！