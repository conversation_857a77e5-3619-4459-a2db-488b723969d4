# ✅ CS-Task-Server外部Dubbo接口封装AI提示词

你是一名资深Java开发工程师，请根据我提供的**外部Dubbo接口文档**，生成符合当前CS-Task-Server项目标准的**外部接口封装代码**，输出要求如下：

## 📌 输出要求：
- **输出格式**：Java源代码，按模块结构输出，可直接粘贴到项目中使用
- **遵循规范**：严格遵循项目规范，包名结构为`com.howbuy.cs.outservice.[系统名]`
- **完整封装**：包含Service类、DTO转换对象（如需要）、Context封装对象（如需要）、异常处理、日志记录
- **生产就绪**：代码质量达到生产级标准，包含完整的错误处理和日志记录

## 📋 外部接口封装规范

### 包结构规范
```
com.howbuy.cs.outservice
├── {外部系统名}/                    # 外部系统包，如auth、crm、accenter等
│   ├── {具体服务名}OuterService.java # 外部服务封装类
│   └── domain/                      # 领域对象包
│       ├── {业务名}Context.java    # 上下文参数类（入参聚合）
│       └── {业务名}DTO.java        # 数据传输对象（返回值封装）
```

### 命名规范
- **封装类名**：`{业务模块名}OuterService`（如`EncryptOuterService`、`SendMessageOuterService`、`QueryAccCenterOuterService`）
- **DTO类名**：`{业务模块名}DTO`（如`UserInfoDTO`、`OrderResultDTO`）
- **Context类名**：`{业务操作名}Context`（如`UserQueryContext`、`OrderCreateContext`）
- **方法名称**：使用业务语义化命名，体现具体功能（如`queryUserInfo`、`updateUserInfo`）

### 代码结构模板

#### 1. 外部接口封装类
```java
package com.howbuy.cs.outservice.{外部系统名};

import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: {系统名}外部接口封装服务
 * <AUTHOR>
 * @date 2025-09-07 16:56:16
 * @since JDK 1.8
 */
@Slf4j
@Service
public class {系统名}OuterService {
    
    @Autowired
    private {外部接口名} {外部接口实例名};
    
    /**
     * @description: {方法功能描述}
     * @param {参数名} {参数说明} （当入参≤4个时使用多个参数）
     * @param context {方法名}Context 方法入参封装对象（当入参>4个时使用）
     * @return {返回值类型} {返回值说明}
     * <AUTHOR>
     * @date 2025-09-07 16:56:16
     * @since JDK 1.8
     */
    public {返回类型} {方法名}({参数列表}) {
        // 参数校验
        if ({参数校验条件}) {
            throw new BusinessException("", "查询参数不能为空");
        }
        
        try {
            // 构建外部接口请求对象
            {请求对象} request = new {请求对象}();
            // 手动赋值转换，禁止使用BeanUtils.copyProperties
            request.set{字段名}({参数值});
            // ... 其他参数转换
            
            log.info("调用外部接口开始，method={}, request={}", 
                "{方法名}", JSON.toJSONString(request));
            
            // 调用外部接口
            {响应对象} response = {外部接口实例名}.execute(request);
            
            // 响应结果校验
            if (response == null || !isSuccess(response)) {
                log.error("外部接口调用失败，request={}, response={}", 
                    JSON.toJSONString(request), JSON.toJSONString(response));
                throw new BusinessException("", "{接口名称}调用失败");
            }
            
            log.info("调用外部接口结束，method={}, response={}", 
                "{方法名}", JSON.toJSONString(response));
            
            // 转换为内部DTO对象
            return convertTo{返回类型}(response);
            
        } catch (Exception e) {
            log.error("外部接口调用异常，request={}, error={}", 
                JSON.toJSONString({参数对象}), e.getMessage(), e);
            throw new BusinessException("", "{接口名称}调用异常");
        }
    }
    
    /**
     * @description: 外部响应对象转换为内部DTO
     * @param response 外部响应对象
     * @return {返回类型} 内部DTO对象
     * <AUTHOR>
     * @date 2025-09-07 16:56:16
     * @since JDK 1.8
     */
    private {返回类型} convertTo{返回类型}({响应对象} response) {
        {返回类型} dto = new {返回类型}();
        // 手动赋值转换，禁止使用BeanUtils.copyProperties
        dto.set{字段名}(response.get{字段名}());
        // ... 其他字段转换
        return dto;
    }
    
    /**
     * @description: 校验外部接口响应是否成功
     * @param response 外部响应对象
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2025-09-07 16:56:16
     * @since JDK 1.8
     */
    private boolean isSuccess({响应对象} response) {
        // 根据具体外部接口响应格式实现
        return response != null && response.isSuccess();
    }
}
```

#### 2. DTO对象（返回参数超过一个字段时使用）
```java
package com.howbuy.cs.outservice.{外部系统名}.domain;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;

/**
 * @description: {DTO功能描述}
 * <AUTHOR>
 * @date 2025-09-07 16:56:16
 * @since JDK 1.8
 */
@Getter
@Setter
public class {业务名}DTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * {字段描述}
     */
    private {字段类型} {字段名};
    
    // ... 其他字段
}
```

#### 3. Context封装对象（当方法入参超过4个时使用）
```java
package com.howbuy.cs.outservice.{外部系统名}.domain;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;

/**
 * @description: {业务操作名}上下文参数
 * <AUTHOR>
 * @date 2025-09-07 16:56:16
 * @since JDK 1.8
 */
@Getter
@Setter
public class {业务操作名}Context implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * {参数1描述}
     */
    private {参数1类型} {参数1名};
    
    /**
     * {参数2描述}
     */
    private {参数2类型} {参数2名};
    
    /**
     * {参数3描述}
     */
    private {参数3类型} {参数3名};
    
    /**
     * {参数4描述}
     */
    private {参数4类型} {参数4名};
    
    /**
     * {参数5描述}
     */
    private {参数5类型} {参数5名};
    
    // ... 其他参数字段
}
```

## 🔧 技术实现要点

### 1. 核心原则
- **封装隔离**: 外部dubbo接口的请求参数和返回参数对象不能直接给业务代码使用，必须进行封装转换
- **参数聚合**: 当方法参数超过4个时，必须创建Context类进行聚合
- **返回封装**: 当返回参数超过一个字段时，必须创建DTO类进行封装
- **统一异常**: 统一异常处理和日志记录规范

### 2. 参数封装规则
- **≤4个参数**: 可直接使用基础类型作为方法参数
- **>4个参数**: 必须创建Context类聚合参数

### 3. 返回值封装规则
- **单个基础类型**: 可直接返回，如 `String`、`Integer`、`Boolean`
- **多个字段**: 必须创建DTO类封装返回

### 4. 依赖注入规范
- 使用 `@Autowired`注解注入外部Dubbo接口
- 外部接口变量命名使用驼峰命名法
- 禁止在外部服务封装类中直接使用外部接口的请求/响应对象暴露给业务层

### 5. 异常处理策略
- **统一异常处理**：捕获所有异常并转换为`BusinessException`
- **详细日志记录**：记录请求参数、响应信息和异常堆栈
- **响应校验**：检查响应对象非空、成功状态、返回码等

### 6. 数据转换
- **禁用BeanUtils.copyProperties**：必须使用手动赋值进行数据转换
- **空值处理**：对外部接口返回的空值进行合理处理
- **类型转换**：注意数据类型的转换，特别是日期、数字等
- **转换方法**：为复杂转换逻辑创建私有转换方法

### 7. 日志规范
- **使用@Slf4j**：统一使用SLF4J日志框架
- **日志级别**：ERROR记录异常，INFO记录重要业务操作，DEBUG记录详细调试信息
- **日志内容**：包含方法名、请求参数、响应信息等关键信息
- **敏感信息**：避免记录敏感数据如密码、证件号等

### 8. 代码风格规范
- **注解使用**：实体类优先使用 `@Setter`/`@Getter`而非 `@Data`，服务类使用 `@Service`注解，日志使用 `@Slf4j`注解
- **工具类使用**：字符串判空使用 `org.apache.commons.lang3.StringUtils.isEmpty`，集合判空使用 `org.apache.commons.collections.CollectionUtils.isEmpty`
- **常量定义**：在封装类中定义相关的业务常量，常量使用全大写，单词间下划线分隔，添加清晰的注释说明

### 9. 性能优化规范
- **批量操作**：优先使用批量接口替代循环单次调用，合理设置批量大小，避免单次请求过大

## 📘 输入内容包含：
由我提供的**外部Dubbo接口文档**，结构如下：
- **外部系统名称**：如auth、crm、accenter等
- **业务模块名称**：如encrypt、message、query等（可选）
- **接口信息**：
  - 接口类名和包名
  - 接口方法名和签名
  - 依赖注入方式
- **请求参数结构**：包括字段名、类型、说明、是否必填
- **响应参数结构**：包括字段名、类型、说明
- **业务场景说明**：接口的具体用途和调用场景
- **异常处理要求**：特殊的异常处理逻辑

## 🧠 你的实现任务：
请根据外部接口文档，生成包括以下内容的完整代码：

1. **外部接口封装类**
   - 正确的包结构和类名
   - @Autowired注解配置
   - 完整的方法实现，包含参数转换、接口调用、异常处理
   - 标准的类和方法注释
   - 根据入参数量选择合适的方法签名（≤4个参数直接传参，>4个参数使用Context封装）

2. **DTO对象类（当返回参数超过一个字段时）**
   - 数据传输对象，用于内外部数据结构转换
   - 使用@Getter/@Setter注解（不使用@Data）
   - 完整的字段注释
   - 实现Serializable接口

3. **Context封装类（当方法入参>4个时）**
   - 方法入参封装对象，类名格式：`{业务操作名}Context`
   - 包含所有方法入参字段，每个字段都有完整注释
   - 使用@Getter/@Setter注解（不使用@Data）
   - 实现Serializable接口
   - 放置在正确的domain包下

4. **必要的常量或枚举（如需要）**
   - 业务相关的枚举值
   - 错误码定义（如项目中不存在）

## 🚫 禁止事项：
| 类型 | 说明 |
|------|------|
| ❌ 使用BeanUtils.copyProperties | 项目规范禁止使用，必须手动赋值 |
| ❌ 缺少异常处理 | 必须有完整的异常处理和日志记录 |
| ❌ 缺少空值校验 | 对外部接口返回值必须进行空值校验 |
| ❌ 使用@Data注解 | DTO对象和Context对象必须使用@Getter/@Setter |
| ❌ 日志随意输出 | 必须使用@Slf4j，禁止使用System.out.println |
| ❌ 入参>4个时不使用Context封装 | 当方法入参超过4个时，必须创建Context类封装 |
| ❌ 返回>1个字段时不使用DTO封装 | 当返回参数超过一个字段时，必须创建DTO类封装 |
| ❌ Context类命名不规范 | Context类必须使用`{业务操作名}Context`格式命名 |
| ❌ DTO类命名不规范 | DTO类必须使用`{业务模块名}DTO`格式命名 |
| ❌ 禁止使用魔法值 | 任何未经定义的字面量都应定义为常量或枚举 |
| ❌ 缺少Serializable接口 | DTO和Context类必须实现Serializable接口 |

## 🎯 使用方式：
在我提供外部Dubbo接口文档后，立即生成符合上述规范的完整Java代码实现，按包结构清晰输出，确保代码可以直接在项目中使用。

## 📝 检查清单：
在开发外部Dubbo接口封装时，请按照以下清单进行自检：

- [ ] 包结构是否符合规范（com.howbuy.cs.outservice.{外部系统名}）
- [ ] 类命名是否符合规范
- [ ] 入参是否正确封装（>4个参数使用Context）
- [ ] 返回值是否正确封装（>1个字段使用DTO）
- [ ] 异常处理是否完整
- [ ] 日志记录是否符合规范
- [ ] 代码风格是否符合项目要求（@Getter/@Setter）
- [ ] 注释是否完整规范（@description、@param、@return、@author、@date、@since）
- [ ] 是否禁止使用BeanUtils.copyProperties
- [ ] 是否使用org.apache.commons.lang3.StringUtils进行字符串判空
- [ ] DTO和Context类是否实现Serializable接口

**特别提醒**：
- 仔细统计每个方法的入参数量
- 当入参≤4个时：使用传统的多参数方法签名
- 当入参>4个时：必须创建对应的Context类进行参数封装
- 当返回参数>1个字段时：必须创建对应的DTO类进行封装
- 所有生成的代码都应包含完整的注释和规范的包结构
- 数据转换必须使用手动赋值，严禁使用BeanUtils.copyProperties
- 使用@Autowired注解进行依赖注入