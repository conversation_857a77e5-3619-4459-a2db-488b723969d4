<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.howbuy.crm</groupId>
        <artifactId>crm-dubbo-framework</artifactId>
        <version>2.0.7.1-RELEASE</version>
    </parent>
    <groupId>com.howbuy.crm</groupId>
    <artifactId>cs-task-server</artifactId>
    <version>2.0.7.1-RELEASE</version>
    <name>cs-task-server</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
        <com.howbuy.crm-core-client.version>2.0.0.3-RELEASE</com.howbuy.crm-core-client.version>
        <com.howbuy.crm-nt-client.version>1.9.2.4-RELEASE</com.howbuy.crm-nt-client.version>
        <com.howbuy.cs-task-client.version>2.0.7.1-RELEASE</com.howbuy.cs-task-client.version>
        <com.howbuy.howbuy-auth-facade.version>2.0.1-RELEASE</com.howbuy.howbuy-auth-facade.version>
        <com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.howbuy-message-service-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-service-2.version>
        <com.howbuy.howbuy-message-rocket-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-rocket-2.version>
        <com.howbuy.howbuy-message-amq-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-amq-2.version>
        <com.howbuy.crm-account-client.version>2.0.0.3-RELEASE</com.howbuy.crm-account-client.version>
        <com.howbuy.howbuy-dfile-service.version>1.18.1-RELEASE</com.howbuy.howbuy-dfile-service.version>
        <com.howbuy.high-order-center-client.version>***********-RELEASE</com.howbuy.high-order-center-client.version>
        <com.howbuy.crm-td-client.version>2.0.0.3-RELEASE</com.howbuy.crm-td-client.version>
        <com.howbuy.howbuy-member-client.version>release-report-2.5-RELEASE</com.howbuy.howbuy-member-client.version>
    <com.howbuy.crm-account.version>2.0.0.3-RELEASE</com.howbuy.crm-account.version>
</properties>


    <dependencies>
        <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-service</artifactId>
            <version>${com.howbuy.howbuy-dfile-service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-impl-webdav</artifactId>
            <version>${com.howbuy.howbuy-dfile-service.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>HowbuyServiceBus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.pa.framework</groupId>
            <artifactId>howbuy-framework-rpc</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hrb</artifactId>
                    <groupId>com.howbuy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zkclient</artifactId>
                    <groupId>com.github.sgroschupf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-stresstester</artifactId>
            <version>1.0.0-RELEASE</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>cs-task-client</artifactId>
            <version>${com.howbuy.cs-task-client.version}</version>
        </dependency>


        <!-- 缓存魔方和彩虹桥 中使用的 zookeeper jar包不同 因此 在使用的时候 排除低版本使用高版本 -->
        <!--<dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>hrb</artifactId>
            <version>1.0.0-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <version>4.2.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuyUtil</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
            <version>1.7.21</version>
        </dependency>

        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>


        <dependency>
            <groupId>com.howbuy.pa.framework</groupId>
            <artifactId>howbuy-framework-rdbms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.howbuy.pa.framework</groupId>
            <artifactId>howbuy-framework-rdbms-oracle</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.0.22</version>
        </dependency>

        <!--jsr 303 -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>1.1.0.Final</version>
        </dependency>
        <!-- hibernate validator -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.2.0.Final</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.pa.cache</groupId>
            <artifactId>howbuy-cache-client</artifactId>
        </dependency>


        <dependency>
            <groupId>com.howbuy.cc.message</groupId>
            <artifactId>message-public-client</artifactId>
            <version>${com.howbuy.message-public-client.version}</version>
        </dependency>
        
        <dependency>
		  <groupId>com.howbuy.crm</groupId>
		  <artifactId>crm-core-client</artifactId>
		  <version>${com.howbuy.crm-core-client.version}</version>
		</dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-member-client</artifactId>
            <version>${com.howbuy.howbuy-member-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-td-client</artifactId>
            <version>${com.howbuy.crm-td-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>high-order-center-client</artifactId>
            <version>${com.howbuy.high-order-center-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-nt-client</artifactId>
            <version>${com.howbuy.crm-nt-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.pa.framework</groupId>
            <artifactId>howbuy-framework-plugin-msg</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>howbuy-message-client</artifactId>
                    <groupId>com.howbuy.pa.basic</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy.acccenter</groupId>
            <artifactId>acc-center-facade</artifactId>
            <version>${com.howbuy.acc-center-facade.version}</version>
            <!-- <exclusions>
                <exclusion>
                    <artifactId>acc-center-common</artifactId>
                    <groupId>com.howbuy.acccenter</groupId>
                </exclusion>
            </exclusions> -->
        </dependency>
        
        <!-- 加解密接口 -->
		<dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-auth-facade</artifactId>
            <version>${com.howbuy.howbuy-auth-facade.version}</version>
        </dependency>

        <!-- CRM导出客户端 -->
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-export-client</artifactId>
            <version>1.8.2.10-RELEASE</version>
        </dependency>
        
        <!-- 日志脱敏 -->
        <dependency>
        	<groupId>com.howbuy.tms</groupId>
    		<artifactId>tms-common-log-pattern</artifactId>
    		<version>1.0.0-SNAPSHOT</version>
    	</dependency>

        <!-- 宙斯 -->
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
            <version>1.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.annotation-api</artifactId>
                    <groupId>javax.annotation</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-cachemanagement</artifactId>
            <version>${com.howbuy.howbuy-cachemanagement.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>activemq-all</artifactId>
                    <groupId>org.apache.activemq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>howbuy-message-amq</artifactId>
                    <groupId>com.howbuy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>howbuy-message-service</artifactId>
                    <groupId>com.howbuy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--MQ消息 from PA-->
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-service-2</artifactId>
            <version>${com.howbuy.howbuy-message-service-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-amq-2</artifactId>
            <version>${com.howbuy.howbuy-message-amq-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-rocket-2</artifactId>
            <version>${com.howbuy.howbuy-message-rocket-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-account-client</artifactId>
            <version>${com.howbuy.crm-account-client.version}</version>
        </dependency>
    </dependencies>

    <build>
        <!-- 解决maven打包mybatis映射文件打包不了的问题 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>

        <plugins>
            <!--<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <finalName>cs-task-server</finalName>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>META-INF/spring.handlers</resource>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>bootstrap.AppStart</mainClass>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>META-INF/spring.schemas</resource>
                                </transformer>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>mofang-config-client.properties</exclude>
                                        <exclude>config_register.properties</exclude>
                                        <exclude>server_url.properties</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>-->

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>mofang-config-client.properties</exclude>
                        <exclude>config_register.properties</exclude>
                        <exclude>server_url.properties</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <useUniqueVersions>false</useUniqueVersions>
                            <addClasspath>true</addClasspath>
                            <mainClass>bootstrap.AppStart</mainClass>
                        </manifest>
                        <manifestEntries>
                            <Package-Stamp>${parelease}</Package-Stamp>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>install</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/cs-task-server/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>