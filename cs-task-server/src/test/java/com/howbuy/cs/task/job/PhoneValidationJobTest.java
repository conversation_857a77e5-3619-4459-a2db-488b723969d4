/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.job;

import com.howbuy.cs.outservice.auth.DecryptOuterService;
import com.howbuy.cs.outservice.crm.mobile.BatchMobileCheckOuterService;
import com.howbuy.cs.outservice.crm.mobile.domain.BatchMobileCheckDTO;
import com.howbuy.cs.outservice.crm.mobile.domain.MobileCheckResultDTO;
import com.howbuy.cs.task.model.CmPhoneValidation;
import com.howbuy.cs.task.service.CmPhoneValidationService;
import com.howbuy.message.SimpleMessage;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description: 手机号验证定时任务测试类
 * @author: hongdong.xie
 * @date: 2025-09-08 14:56:58
 * @since JDK 1.8
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class PhoneValidationJobTest {

    @InjectMocks
    private PhoneValidationJob phoneValidationJob;

    @Mock
    private CmPhoneValidationService cmPhoneValidationService;

    @Mock
    private DecryptOuterService decryptOuterService;

    @Mock
    private BatchMobileCheckOuterService batchMobileCheckOuterService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 设置批次大小
        ReflectionTestUtils.setField(phoneValidationJob, "batchSize", 100);
    }

    @Test
    public void testDoProcessMessage_Success() {
        // 准备测试数据
        List<CmPhoneValidation> mockRecords = createMockRecords();
        Map<String, String> mockDecryptResult = createMockDecryptResult();
        BatchMobileCheckDTO mockCheckResult = createMockCheckResult();

        // Mock服务调用
        when(cmPhoneValidationService.getUnprocessedRecords(anyInt())).thenReturn(mockRecords);
        when(cmPhoneValidationService.markRecordsAsProcessing(anyList())).thenReturn(true);
        when(decryptOuterService.batchDecrypt(anyList())).thenReturn(mockDecryptResult);
        when(batchMobileCheckOuterService.batchCheckMobile(anyList(), anyInt())).thenReturn(mockCheckResult);
        when(cmPhoneValidationService.batchUpdateValidationResult(anyList())).thenReturn(true);

        // 执行测试
        SimpleMessage message = new SimpleMessage();
        message.setContent("{}");
        phoneValidationJob.doProcessMessage(message);

        // 验证调用
        verify(cmPhoneValidationService, times(1)).getUnprocessedRecords(100);
        verify(cmPhoneValidationService, times(1)).markRecordsAsProcessing(mockRecords);
        verify(decryptOuterService, times(1)).batchDecrypt(anyList());
        verify(batchMobileCheckOuterService, times(1)).batchCheckMobile(anyList(), eq(0));
        verify(cmPhoneValidationService, times(1)).batchUpdateValidationResult(anyList());
    }

    @Test
    public void testDoProcessMessage_NoRecords() {
        // Mock没有待处理记录
        when(cmPhoneValidationService.getUnprocessedRecords(anyInt())).thenReturn(Collections.emptyList());

        // 执行测试
        SimpleMessage message = new SimpleMessage();
        message.setContent("{}");
        phoneValidationJob.doProcessMessage(message);

        // 验证只调用了查询方法
        verify(cmPhoneValidationService, times(1)).getUnprocessedRecords(100);
        verify(cmPhoneValidationService, never()).markRecordsAsProcessing(anyList());
        verify(decryptOuterService, never()).batchDecrypt(anyList());
        verify(batchMobileCheckOuterService, never()).batchCheckMobile(anyList(), anyInt());
    }

    @Test
    public void testDoProcessMessage_MarkProcessingFailed() {
        // 准备测试数据
        List<CmPhoneValidation> mockRecords = createMockRecords();

        // Mock服务调用
        when(cmPhoneValidationService.getUnprocessedRecords(anyInt())).thenReturn(mockRecords);
        when(cmPhoneValidationService.markRecordsAsProcessing(anyList())).thenReturn(false);

        // 执行测试
        SimpleMessage message = new SimpleMessage();
        message.setContent("{}");
        phoneValidationJob.doProcessMessage(message);

        // 验证调用
        verify(cmPhoneValidationService, times(1)).getUnprocessedRecords(100);
        verify(cmPhoneValidationService, times(1)).markRecordsAsProcessing(mockRecords);
        // 标记失败后不应该继续执行后续步骤
        verify(decryptOuterService, never()).batchDecrypt(anyList());
        verify(batchMobileCheckOuterService, never()).batchCheckMobile(anyList(), anyInt());
    }

    @Test
    public void testGetQuartMessageChannel() {
        String channel = phoneValidationJob.getQuartMessageChannel();
        assert "phone_validation_job".equals(channel);
    }

    @Test
    public void testGetExpireSecond() {
        int expireSecond = phoneValidationJob.getExpireSecond();
        assert expireSecond == 1800;
    }

    /**
     * 创建模拟记录
     */
    private List<CmPhoneValidation> createMockRecords() {
        List<CmPhoneValidation> records = new ArrayList<>();
        
        CmPhoneValidation record1 = new CmPhoneValidation();
        record1.setId(1L);
        record1.setConsCustNo("CUST001");
        record1.setMobileEncrypted("encrypted_mobile_001");
        record1.setProcessFlag(0);
        record1.setCreateTime(new Date());
        record1.setUpdateTime(new Date());
        records.add(record1);

        CmPhoneValidation record2 = new CmPhoneValidation();
        record2.setId(2L);
        record2.setConsCustNo("CUST002");
        record2.setMobileEncrypted("encrypted_mobile_002");
        record2.setProcessFlag(0);
        record2.setCreateTime(new Date());
        record2.setUpdateTime(new Date());
        records.add(record2);

        return records;
    }

    /**
     * 创建模拟解密结果
     */
    private Map<String, String> createMockDecryptResult() {
        Map<String, String> decryptResult = new HashMap<>();
        decryptResult.put("encrypted_mobile_001", "13800138001");
        decryptResult.put("encrypted_mobile_002", "13800138002");
        return decryptResult;
    }

    /**
     * 创建模拟验证结果
     */
    private BatchMobileCheckDTO createMockCheckResult() {
        BatchMobileCheckDTO checkResult = new BatchMobileCheckDTO();
        
        List<MobileCheckResultDTO> results = new ArrayList<>();
        
        MobileCheckResultDTO result1 = new MobileCheckResultDTO();
        result1.setMobile("13800138001");
        result1.setArea("北京-北京");
        result1.setNumberType("中国移动");
        result1.setStatus(1);
        result1.setStatusDesc("实号");
        result1.setLastTime(String.valueOf(System.currentTimeMillis()));
        results.add(result1);

        MobileCheckResultDTO result2 = new MobileCheckResultDTO();
        result2.setMobile("13800138002");
        result2.setArea("上海-上海");
        result2.setNumberType("中国联通");
        result2.setStatus(0);
        result2.setStatusDesc("空号");
        result2.setLastTime(String.valueOf(System.currentTimeMillis()));
        results.add(result2);

        checkResult.setCheckResults(results);
        checkResult.setTotalCount(2);
        checkResult.setSuccessCount(2);
        checkResult.setFailCount(0);

        return checkResult;
    }
}
