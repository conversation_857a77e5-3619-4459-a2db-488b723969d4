-- 手机号可用性校验任务表
-- 作者: hongdong.xie
-- 日期: 2025-09-08 14:56:58

-- 创建表
CREATE TABLE CM_PHONE_VALIDATION (
    ID NUMBER(19) NOT NULL,
    CONS_CUST_NO VARCHAR2(32) NOT NULL,
    MOBILE_ENCRYPTED VARCHAR2(128) NOT NULL,
    CHECK_RESULT NUMBER(1),
    PROCESS_FLAG NUMBER(1) DEFAULT 0 NOT NULL,
    PROCESS_REMARK VARCHAR2(500),
    AREA VARCHAR2(100),
    OPERATOR_TYPE VARCHAR2(50),
    CREATE_TIME DATE DEFAULT SYSDATE NOT NULL,
    UPDATE_TIME DATE DEFAULT SYSDATE NOT NULL,
    CONSTRAINT PK_CM_PHONE_VALIDATION PRIMARY KEY (ID)
);

-- 添加表注释
COMMENT ON TABLE CM_PHONE_VALIDATION IS '手机号可用性校验任务表';

-- 添加字段注释
COMMENT ON COLUMN CM_PHONE_VALIDATION.ID IS '唯一主键，通过序列生成';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CONS_CUST_NO IS '投顾客户号';
COMMENT ON COLUMN CM_PHONE_VALIDATION.MOBILE_ENCRYPTED IS '加密的手机号';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CHECK_RESULT IS '校验结果 (0:空号, 1:实号, 2:停机, 3:库无, 4:沉默号, 5:风险号)';
COMMENT ON COLUMN CM_PHONE_VALIDATION.PROCESS_FLAG IS '处理标记 (0:未处理, 1:处理中, 2:处理完成, 3:处理失败)';
COMMENT ON COLUMN CM_PHONE_VALIDATION.PROCESS_REMARK IS '处理失败时的备注信息';
COMMENT ON COLUMN CM_PHONE_VALIDATION.AREA IS '手机号归属地';
COMMENT ON COLUMN CM_PHONE_VALIDATION.OPERATOR_TYPE IS '运营商类型';
COMMENT ON COLUMN CM_PHONE_VALIDATION.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CM_PHONE_VALIDATION.UPDATE_TIME IS '最后更新时间';

-- 创建序列
CREATE SEQUENCE SEQ_CM_PHONE_VALIDATION
    START WITH 1
    INCREMENT BY 1
    NOMAXVALUE
    NOCYCLE
    CACHE 20;

-- 创建索引
-- 处理标记索引（用于查询待处理记录）
CREATE INDEX IDX_CM_PHONE_VALIDATION_FLAG ON CM_PHONE_VALIDATION(PROCESS_FLAG);

-- 客户号索引（用于按客户查询）
CREATE INDEX IDX_CM_PHONE_VALIDATION_CUST ON CM_PHONE_VALIDATION(CONS_CUST_NO);

-- 加密手机号索引（用于去重查询）
CREATE INDEX IDX_CM_PHONE_VALIDATION_MOBILE ON CM_PHONE_VALIDATION(MOBILE_ENCRYPTED);

-- 创建时间索引（用于按时间查询）
CREATE INDEX IDX_CM_PHONE_VALIDATION_TIME ON CM_PHONE_VALIDATION(CREATE_TIME);

-- 组合索引（处理标记+创建时间，用于定时任务查询）
CREATE INDEX IDX_CM_PHONE_VALIDATION_FLAG_TIME ON CM_PHONE_VALIDATION(PROCESS_FLAG, CREATE_TIME);

-- 插入测试数据（可选）
/*
INSERT INTO CM_PHONE_VALIDATION (
    ID, CONS_CUST_NO, MOBILE_ENCRYPTED, PROCESS_FLAG, CREATE_TIME, UPDATE_TIME
) VALUES (
    SEQ_CM_PHONE_VALIDATION.NEXTVAL, 
    'TEST001', 
    'encrypted_mobile_test_001', 
    0, 
    SYSDATE, 
    SYSDATE
);

INSERT INTO CM_PHONE_VALIDATION (
    ID, CONS_CUST_NO, MOBILE_ENCRYPTED, PROCESS_FLAG, CREATE_TIME, UPDATE_TIME
) VALUES (
    SEQ_CM_PHONE_VALIDATION.NEXTVAL, 
    'TEST002', 
    'encrypted_mobile_test_002', 
    0, 
    SYSDATE, 
    SYSDATE
);

COMMIT;
*/
