# 手机号可用性校验定时任务

## 概述

手机号可用性校验定时任务用于批量验证客户手机号的可用性状态，包括空号、实号、停机等状态检测。

**作者**: hongdong.xie  
**日期**: 2025-09-08 14:56:58  
**版本**: 1.0.0

## 功能特性

- **批量处理**: 支持批量查询和处理手机号验证
- **加密解密**: 集成认证系统的批量解密功能
- **状态检测**: 支持多种手机号状态检测（空号、实号、停机、库无、沉默号、风险号）
- **异常处理**: 完善的异常处理和重试机制
- **监控日志**: 详细的执行日志和监控指标

## 技术架构

### 核心组件

1. **PhoneValidationJob**: 定时任务主类，继承AbstractBatchMessageJob
2. **CmPhoneValidationService**: 数据库操作服务层
3. **DecryptOuterService**: 手机号解密外部服务封装
4. **BatchMobileCheckOuterService**: 手机号验证外部服务封装

### 数据流程

```
1. 查询待处理记录 → 2. 标记为处理中 → 3. 批量解密手机号 → 4. 批量验证手机号 → 5. 更新验证结果
```

## 数据库设计

### 表结构: CM_PHONE_VALIDATION

| 字段名 | 类型 | 说明 |
|--------|------|------|
| ID | NUMBER(19) | 主键ID |
| CONS_CUST_NO | VARCHAR2(32) | 投顾客户号 |
| MOBILE_ENCRYPTED | VARCHAR2(128) | 加密手机号 |
| CHECK_RESULT | NUMBER(1) | 验证结果 |
| PROCESS_FLAG | NUMBER(1) | 处理标记 |
| PROCESS_REMARK | VARCHAR2(500) | 处理备注 |
| AREA | VARCHAR2(100) | 归属地 |
| OPERATOR_TYPE | VARCHAR2(50) | 运营商类型 |
| CREATE_TIME | DATE | 创建时间 |
| UPDATE_TIME | DATE | 更新时间 |

### 验证结果枚举

- **0**: 空号
- **1**: 实号
- **2**: 停机
- **3**: 库无
- **4**: 沉默号
- **5**: 风险号

### 处理标记枚举

- **0**: 未处理
- **1**: 处理中
- **2**: 处理完成
- **3**: 处理失败

## 配置说明

### 应用配置

```properties
# 批次处理数量
phone.validation.batch.size=100

# 任务超时时间（秒）
phone.validation.job.timeout=1800

# 重试配置
phone.validation.retry.max.times=3
phone.validation.retry.interval=60
```

### 调度配置

- **消息队列**: phone_validation_job
- **锁过期时间**: 1800秒（30分钟）
- **建议执行频率**: 每5分钟执行一次

## 部署说明

### 1. 数据库初始化

```sql
-- 执行建表SQL
@cs-task-server/src/main/resources/sql/cm_phone_validation.sql
```

### 2. 依赖检查

确保以下外部服务可用：
- howbuy-auth系统（解密服务）
- crm-export系统（手机号验证服务）

### 3. 配置调度中心

在调度中心配置定时任务：
- 任务名称: 手机号可用性校验
- 消息队列: phone_validation_job
- 执行频率: 0 */5 * * * ?（每5分钟）

## 使用方式

### 1. 数据准备

向CM_PHONE_VALIDATION表插入待验证的手机号记录：

```sql
INSERT INTO CM_PHONE_VALIDATION (
    ID, CONS_CUST_NO, MOBILE_ENCRYPTED, PROCESS_FLAG
) VALUES (
    SEQ_CM_PHONE_VALIDATION.NEXTVAL,
    '客户号',
    '加密手机号',
    0
);
```

### 2. 任务执行

定时任务会自动执行，也可以通过调度中心手动触发。

### 3. 结果查询

```sql
-- 查询验证结果
SELECT 
    CONS_CUST_NO,
    CHECK_RESULT,
    AREA,
    OPERATOR_TYPE,
    PROCESS_FLAG,
    PROCESS_REMARK
FROM CM_PHONE_VALIDATION
WHERE PROCESS_FLAG = 2; -- 处理完成
```

## 监控指标

### 关键指标

- **处理成功率**: 处理完成记录数 / 总处理记录数
- **平均处理时间**: 单批次平均执行时间
- **错误率**: 处理失败记录数 / 总处理记录数

### 日志监控

- 任务执行开始/结束日志
- 批量处理数量统计
- 异常错误详情
- 外部接口调用耗时

## 故障排查

### 常见问题

1. **任务执行失败**
   - 检查外部服务连通性
   - 查看详细错误日志
   - 确认数据库连接状态

2. **处理速度慢**
   - 调整批次大小配置
   - 检查外部接口响应时间
   - 优化数据库索引

3. **数据不一致**
   - 检查事务配置
   - 确认批量更新逻辑
   - 验证数据完整性

### 日志位置

- 应用日志: logs/cs-task-server.log
- 任务执行日志: 搜索关键字 "PhoneValidationJob"

## 扩展说明

### 性能优化

1. **批次大小调优**: 根据系统负载调整batch.size
2. **并发处理**: 可考虑多线程并发处理
3. **缓存优化**: 对重复验证的手机号进行缓存

### 功能扩展

1. **结果通知**: 验证完成后发送通知
2. **统计报表**: 生成验证结果统计报表
3. **历史数据**: 保留历史验证记录

## 联系方式

如有问题请联系：hongdong.xie
