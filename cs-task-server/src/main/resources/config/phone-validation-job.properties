# 手机号验证定时任务配置
# 作者: hongdong.xie
# 日期: 2025-09-08 14:56:58

# 批次处理数量配置
phone.validation.batch.size=100

# 定时任务执行频率配置（示例：每5分钟执行一次）
# 实际使用时需要在调度中心配置
phone.validation.job.cron=0 */5 * * * ?

# 任务超时时间配置（秒）
phone.validation.job.timeout=1800

# 重试配置
phone.validation.retry.max.times=3
phone.validation.retry.interval=60

# 日志配置
phone.validation.log.level=INFO
phone.validation.log.detail.enabled=true

# 监控配置
phone.validation.monitor.enabled=true
phone.validation.alert.threshold=1000
