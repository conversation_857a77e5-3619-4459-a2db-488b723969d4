/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.enums;

/**
 * @description: 手机号验证相关枚举
 * @author: hongdong.xie
 * @date: 2025-09-08 14:56:58
 * @since JDK 1.8
 */
public class PhoneValidationEnum {

    /**
     * 处理标记枚举
     */
    public enum ProcessFlag {
        /**
         * 未处理
         */
        UNPROCESSED(0, "未处理"),
        /**
         * 处理中
         */
        PROCESSING(1, "处理中"),
        /**
         * 处理完成
         */
        COMPLETED(2, "处理完成"),
        /**
         * 处理失败
         */
        FAILED(3, "处理失败");

        private final Integer code;
        private final String desc;

        ProcessFlag(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ProcessFlag getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (ProcessFlag flag : values()) {
                if (flag.getCode().equals(code)) {
                    return flag;
                }
            }
            return null;
        }
    }

    /**
     * 手机号验证结果枚举
     */
    public enum CheckResult {
        /**
         * 空号
         */
        EMPTY(0, "空号"),
        /**
         * 实号
         */
        ACTIVE(1, "实号"),
        /**
         * 停机
         */
        SUSPENDED(2, "停机"),
        /**
         * 库无
         */
        NOT_FOUND(3, "库无"),
        /**
         * 沉默号
         */
        SILENT(4, "沉默号"),
        /**
         * 风险号
         */
        RISK(5, "风险号");

        private final Integer code;
        private final String desc;

        CheckResult(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static CheckResult getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (CheckResult result : values()) {
                if (result.getCode().equals(code)) {
                    return result;
                }
            }
            return null;
        }

        /**
         * @description: 判断是否为有效手机号（实号）
         * @param code 验证结果代码
         * @return boolean 是否为有效手机号
         * @author: hongdong.xie
         * @date: 2025-09-08 14:56:58
         * @since JDK 1.8
         */
        public static boolean isValidMobile(Integer code) {
            return ACTIVE.getCode().equals(code);
        }

        /**
         * @description: 判断是否为无效手机号（空号、停机、风险号等）
         * @param code 验证结果代码
         * @return boolean 是否为无效手机号
         * @author: hongdong.xie
         * @date: 2025-09-08 14:56:58
         * @since JDK 1.8
         */
        public static boolean isInvalidMobile(Integer code) {
            return EMPTY.getCode().equals(code) || 
                   SUSPENDED.getCode().equals(code) || 
                   RISK.getCode().equals(code);
        }
    }
}
