/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.howbuy.cs.task.model.CmPhoneValidation;

import java.util.List;

/**
 * @description: 手机号验证服务接口
 * @author: hongdong.xie
 * @date: 2025-09-08 14:56:58
 * @since JDK 1.8
 */
public interface CmPhoneValidationService {

    /**
     * @description: 查询待处理的手机号验证记录
     * @param limit 查询数量限制
     * @return List<CmPhoneValidation> 待处理记录列表
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    List<CmPhoneValidation> getUnprocessedRecords(Integer limit);

    /**
     * @description: 批量更新处理标记为处理中
     * @param records 记录列表
     * @return boolean 更新是否成功
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    boolean markRecordsAsProcessing(List<CmPhoneValidation> records);

    /**
     * @description: 批量更新验证结果
     * @param records 更新记录列表
     * @return boolean 更新是否成功
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    boolean batchUpdateValidationResult(List<CmPhoneValidation> records);

    /**
     * @description: 单条更新验证结果（批量更新失败时使用）
     * @param record 更新记录
     * @return boolean 更新是否成功
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    boolean updateValidationResultById(CmPhoneValidation record);
}
