/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmPhoneValidation;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CmPhoneValidationMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CmPhoneValidation record);

    int insertSelective(CmPhoneValidation record);

    CmPhoneValidation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CmPhoneValidation record);

    int updateByPrimaryKey(CmPhoneValidation record);

    /**
     * @description: 查询待处理的手机号验证记录
     * @param processFlag 处理标记
     * @param limit 查询数量限制
     * @return List<CmPhoneValidation> 待处理记录列表
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    List<CmPhoneValidation> selectUnprocessedRecords(@Param("processFlag") Integer processFlag, 
                                                     @Param("limit") Integer limit);

    /**
     * @description: 批量更新处理标记为处理中
     * @param ids ID列表
     * @param processFlag 处理标记
     * @return int 更新记录数
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    int batchUpdateProcessFlag(@Param("ids") List<Long> ids, 
                               @Param("processFlag") Integer processFlag);

    /**
     * @description: 批量更新验证结果
     * @param records 更新记录列表
     * @return int 更新记录数
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    int batchUpdateValidationResult(@Param("records") List<CmPhoneValidation> records);

    /**
     * @description: 单条更新验证结果（批量更新失败时使用）
     * @param record 更新记录
     * @return int 更新记录数
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    int updateValidationResultById(CmPhoneValidation record);
}
