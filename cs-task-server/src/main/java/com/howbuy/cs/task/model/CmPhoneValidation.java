/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 手机号可用性校验任务表
 */
public class CmPhoneValidation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键，通过序列生成
     */
    private Long id;

    /**
     * 投顾客户号
     */
    private String consCustNo;

    /**
     * 加密的手机号
     */
    private String mobileEncrypted;

    /**
     * 校验结果 (0:空号, 1:实号, 2:停机, 3:库无, 4:沉默号, 5:风险号)
     */
    private Integer checkResult;

    /**
     * 处理标记 (0:未处理, 1:处理中, 2:处理完成, 3:处理失败)
     */
    private Integer processFlag;

    /**
     * 处理失败时的备注信息
     */
    private String processRemark;

    /**
     * 手机号归属地
     */
    private String area;

    /**
     * 运营商类型
     */
    private String operatorType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConsCustNo() {
        return consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }

    public String getMobileEncrypted() {
        return mobileEncrypted;
    }

    public void setMobileEncrypted(String mobileEncrypted) {
        this.mobileEncrypted = mobileEncrypted;
    }

    public Integer getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(Integer checkResult) {
        this.checkResult = checkResult;
    }

    public Integer getProcessFlag() {
        return processFlag;
    }

    public void setProcessFlag(Integer processFlag) {
        this.processFlag = processFlag;
    }

    public String getProcessRemark() {
        return processRemark;
    }

    public void setProcessRemark(String processRemark) {
        this.processRemark = processRemark;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
