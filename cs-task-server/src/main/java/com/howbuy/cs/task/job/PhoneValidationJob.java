/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.job;

import com.alibaba.fastjson.JSON;
import com.howbuy.cs.job.AbstractBatchMessageJob;
import com.howbuy.cs.outservice.auth.DecryptOuterService;
import com.howbuy.cs.outservice.crm.mobile.BatchMobileCheckOuterService;
import com.howbuy.cs.outservice.crm.mobile.domain.BatchMobileCheckDTO;
import com.howbuy.cs.outservice.crm.mobile.domain.MobileCheckResultDTO;
import com.howbuy.cs.task.model.CmPhoneValidation;
import com.howbuy.cs.task.service.CmPhoneValidationService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 手机号可用性校验定时任务
 * @author: hongdong.xie
 * @date: 2025-09-08 14:56:58
 * @since JDK 1.8
 */
@Slf4j
@Component
public class PhoneValidationJob extends AbstractBatchMessageJob {

    /**
     * 消息队列名称
     */
    private static final String MESSAGE_CHANNEL = "phone_validation_job";

    /**
     * 处理标记：未处理
     */
    private static final Integer PROCESS_FLAG_UNPROCESSED = 0;

    /**
     * 处理标记：处理中
     */
    private static final Integer PROCESS_FLAG_PROCESSING = 1;

    /**
     * 处理标记：处理完成
     */
    private static final Integer PROCESS_FLAG_COMPLETED = 2;

    /**
     * 处理标记：处理失败
     */
    private static final Integer PROCESS_FLAG_FAILED = 3;

    /**
     * 每批次处理数量，默认100
     */
    @Value("${phone.validation.batch.size:100}")
    private Integer batchSize;

    @Autowired
    private CmPhoneValidationService cmPhoneValidationService;

    @Autowired
    private DecryptOuterService decryptOuterService;

    @Autowired
    private BatchMobileCheckOuterService batchMobileCheckOuterService;

    @Override
    protected String getQuartMessageChannel() {
        return MESSAGE_CHANNEL;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("手机号验证定时任务开始执行, message={}", getContent(message));

        try {
            // 1. 查询待处理的记录
            List<CmPhoneValidation> unprocessedRecords = cmPhoneValidationService.getUnprocessedRecords(batchSize);
            if (CollectionUtils.isEmpty(unprocessedRecords)) {
                log.info("没有待处理的手机号验证记录");
                return;
            }

            log.info("查询到待处理记录数量: {}", unprocessedRecords.size());

            // 2. 标记为处理中
            boolean markSuccess = cmPhoneValidationService.markRecordsAsProcessing(unprocessedRecords);
            if (!markSuccess) {
                log.error("标记记录为处理中失败");
                return;
            }

            // 3. 批量解密手机号
            List<String> encryptedMobiles = unprocessedRecords.stream()
                    .map(CmPhoneValidation::getMobileEncrypted)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            Map<String, String> decryptResult = decryptOuterService.batchDecrypt(encryptedMobiles);
            log.info("批量解密完成, 解密数量: {}", decryptResult.size());

            // 4. 批量验证手机号
            List<String> decryptedMobiles = new ArrayList<>(decryptResult.values());
            BatchMobileCheckDTO checkResult = batchMobileCheckOuterService.batchCheckMobile(decryptedMobiles, 0);
            log.info("批量验证完成, 验证数量: {}", checkResult.getTotalCount());

            // 5. 更新验证结果
            updateValidationResults(unprocessedRecords, decryptResult, checkResult);

            log.info("手机号验证定时任务执行完成");

        } catch (Exception e) {
            log.error("手机号验证定时任务执行异常", e);
            throw e;
        }
    }

    /**
     * @description: 更新验证结果
     * @param originalRecords 原始记录列表
     * @param decryptResult 解密结果
     * @param checkResult 验证结果
     * @author: hongdong.xie
     * @date: 2025-09-08 14:56:58
     * @since JDK 1.8
     */
    private void updateValidationResults(List<CmPhoneValidation> originalRecords,
                                       Map<String, String> decryptResult,
                                       BatchMobileCheckDTO checkResult) {
        try {
            // 构建验证结果映射 (明文手机号 -> 验证结果)
            Map<String, MobileCheckResultDTO> mobileToResultMap = null;
            if (checkResult != null && CollectionUtils.isNotEmpty(checkResult.getCheckResults())) {
                mobileToResultMap = checkResult.getCheckResults().stream()
                        .collect(Collectors.toMap(
                                MobileCheckResultDTO::getMobile,
                                result -> result,
                                (existing, replacement) -> existing
                        ));
            }

            // 更新每条记录的验证结果
            List<CmPhoneValidation> updateRecords = new ArrayList<>();
            for (CmPhoneValidation record : originalRecords) {
                String encryptedMobile = record.getMobileEncrypted();
                String decryptedMobile = decryptResult.get(encryptedMobile);

                if (StringUtils.isNotBlank(decryptedMobile) && mobileToResultMap != null) {
                    MobileCheckResultDTO result = mobileToResultMap.get(decryptedMobile);
                    if (result != null) {
                        // 设置验证结果
                        record.setCheckResult(result.getStatus());
                        record.setArea(result.getArea());
                        record.setOperatorType(result.getNumberType());
                        record.setProcessFlag(PROCESS_FLAG_COMPLETED);
                        record.setProcessRemark("验证成功");
                    } else {
                        // 验证失败
                        record.setProcessFlag(PROCESS_FLAG_FAILED);
                        record.setProcessRemark("验证接口未返回结果");
                    }
                } else {
                    // 解密失败
                    record.setProcessFlag(PROCESS_FLAG_FAILED);
                    record.setProcessRemark("手机号解密失败");
                }

                record.setUpdateTime(new Date());
                updateRecords.add(record);
            }

            // 批量更新
            boolean batchUpdateSuccess = cmPhoneValidationService.batchUpdateValidationResult(updateRecords);
            if (!batchUpdateSuccess) {
                log.warn("批量更新失败，尝试逐条更新");
                // 批量更新失败时，逐条更新
                for (CmPhoneValidation record : updateRecords) {
                    try {
                        cmPhoneValidationService.updateValidationResultById(record);
                    } catch (Exception e) {
                        log.error("单条更新失败, id={}, error={}", record.getId(), e.getMessage(), e);
                    }
                }
            }

            log.info("验证结果更新完成, 更新记录数: {}", updateRecords.size());

        } catch (Exception e) {
            log.error("更新验证结果异常", e);
            throw e;
        }
    }

    @Override
    protected int getExpireSecond() {
        // 手机号验证任务可能耗时较长，设置锁过期时间为1800秒（30分钟）
        return 1800;
    }
}
