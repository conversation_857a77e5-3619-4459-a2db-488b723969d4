/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service.impl;

import com.howbuy.cs.task.dao.CmPhoneValidationMapper;
import com.howbuy.cs.task.model.CmPhoneValidation;
import com.howbuy.cs.task.service.CmPhoneValidationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 手机号验证服务实现类
 * @author: hongdong.xie
 * @date: 2025-09-08 14:56:58
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmPhoneValidationServiceImpl implements CmPhoneValidationService {

    /**
     * 处理标记：未处理
     */
    private static final Integer PROCESS_FLAG_UNPROCESSED = 0;

    /**
     * 处理标记：处理中
     */
    private static final Integer PROCESS_FLAG_PROCESSING = 1;

    @Autowired
    private CmPhoneValidationMapper cmPhoneValidationMapper;

    @Override
    public List<CmPhoneValidation> getUnprocessedRecords(Integer limit) {
        try {
            return cmPhoneValidationMapper.selectUnprocessedRecords(PROCESS_FLAG_UNPROCESSED, limit);
        } catch (Exception e) {
            log.error("查询待处理记录失败, limit={}, error={}", limit, e.getMessage(), e);
            throw new RuntimeException("查询待处理记录失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markRecordsAsProcessing(List<CmPhoneValidation> records) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }

        try {
            List<Long> ids = records.stream()
                    .map(CmPhoneValidation::getId)
                    .collect(Collectors.toList());

            int updateCount = cmPhoneValidationMapper.batchUpdateProcessFlag(ids, PROCESS_FLAG_PROCESSING);
            log.info("批量更新处理标记成功, 更新记录数={}", updateCount);
            return updateCount > 0;
        } catch (Exception e) {
            log.error("批量更新处理标记失败, recordCount={}, error={}", records.size(), e.getMessage(), e);
            throw new RuntimeException("批量更新处理标记失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateValidationResult(List<CmPhoneValidation> records) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }

        try {
            int updateCount = cmPhoneValidationMapper.batchUpdateValidationResult(records);
            log.info("批量更新验证结果成功, 更新记录数={}", updateCount);
            return updateCount > 0;
        } catch (Exception e) {
            log.error("批量更新验证结果失败, recordCount={}, error={}", records.size(), e.getMessage(), e);
            throw new RuntimeException("批量更新验证结果失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateValidationResultById(CmPhoneValidation record) {
        if (record == null || record.getId() == null) {
            return false;
        }

        try {
            int updateCount = cmPhoneValidationMapper.updateValidationResultById(record);
            log.info("单条更新验证结果成功, id={}, updateCount={}", record.getId(), updateCount);
            return updateCount > 0;
        } catch (Exception e) {
            log.error("单条更新验证结果失败, id={}, error={}", record.getId(), e.getMessage(), e);
            throw new RuntimeException("单条更新验证结果失败", e);
        }
    }
}
