<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmPhoneValidationMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmPhoneValidation">
    <!--@mbg.generated-->
    <!--@Table CM_PHONE_VALIDATION-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CONS_CUST_NO" jdbcType="VARCHAR" property="consCustNo" />
    <result column="MOBILE_ENCRYPTED" jdbcType="VARCHAR" property="mobileEncrypted" />
    <result column="CHECK_RESULT" jdbcType="DECIMAL" property="checkResult" />
    <result column="PROCESS_FLAG" jdbcType="DECIMAL" property="processFlag" />
    <result column="PROCESS_REMARK" jdbcType="VARCHAR" property="processRemark" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="OPERATOR_TYPE" jdbcType="VARCHAR" property="operatorType" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONS_CUST_NO, MOBILE_ENCRYPTED, CHECK_RESULT, PROCESS_FLAG, PROCESS_REMARK, 
    AREA, OPERATOR_TYPE, CREATE_TIME, UPDATE_TIME
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_PHONE_VALIDATION
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from CM_PHONE_VALIDATION
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmPhoneValidation">
    <!--@mbg.generated-->
    insert into CM_PHONE_VALIDATION (ID, CONS_CUST_NO, MOBILE_ENCRYPTED, 
      CHECK_RESULT, PROCESS_FLAG, PROCESS_REMARK, 
      AREA, OPERATOR_TYPE, CREATE_TIME, 
      UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{consCustNo,jdbcType=VARCHAR}, #{mobileEncrypted,jdbcType=VARCHAR}, 
      #{checkResult,jdbcType=DECIMAL}, #{processFlag,jdbcType=DECIMAL}, #{processRemark,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{operatorType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmPhoneValidation">
    <!--@mbg.generated-->
    insert into CM_PHONE_VALIDATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="consCustNo != null">
        CONS_CUST_NO,
      </if>
      <if test="mobileEncrypted != null">
        MOBILE_ENCRYPTED,
      </if>
      <if test="checkResult != null">
        CHECK_RESULT,
      </if>
      <if test="processFlag != null">
        PROCESS_FLAG,
      </if>
      <if test="processRemark != null">
        PROCESS_REMARK,
      </if>
      <if test="area != null">
        AREA,
      </if>
      <if test="operatorType != null">
        OPERATOR_TYPE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="consCustNo != null">
        #{consCustNo,jdbcType=VARCHAR},
      </if>
      <if test="mobileEncrypted != null">
        #{mobileEncrypted,jdbcType=VARCHAR},
      </if>
      <if test="checkResult != null">
        #{checkResult,jdbcType=DECIMAL},
      </if>
      <if test="processFlag != null">
        #{processFlag,jdbcType=DECIMAL},
      </if>
      <if test="processRemark != null">
        #{processRemark,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.CmPhoneValidation">
    <!--@mbg.generated-->
    update CM_PHONE_VALIDATION
    <set>
      <if test="consCustNo != null">
        CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR},
      </if>
      <if test="mobileEncrypted != null">
        MOBILE_ENCRYPTED = #{mobileEncrypted,jdbcType=VARCHAR},
      </if>
      <if test="checkResult != null">
        CHECK_RESULT = #{checkResult,jdbcType=DECIMAL},
      </if>
      <if test="processFlag != null">
        PROCESS_FLAG = #{processFlag,jdbcType=DECIMAL},
      </if>
      <if test="processRemark != null">
        PROCESS_REMARK = #{processRemark,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        OPERATOR_TYPE = #{operatorType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.CmPhoneValidation">
    <!--@mbg.generated-->
    update CM_PHONE_VALIDATION
    set CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR},
        MOBILE_ENCRYPTED = #{mobileEncrypted,jdbcType=VARCHAR},
        CHECK_RESULT = #{checkResult,jdbcType=DECIMAL},
        PROCESS_FLAG = #{processFlag,jdbcType=DECIMAL},
        PROCESS_REMARK = #{processRemark,jdbcType=VARCHAR},
        AREA = #{area,jdbcType=VARCHAR},
        OPERATOR_TYPE = #{operatorType,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <!-- 自定义查询方法 -->
  <select id="selectUnprocessedRecords" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CM_PHONE_VALIDATION
    where PROCESS_FLAG = #{processFlag,jdbcType=DECIMAL}
    order by ID
    <if test="limit != null and limit > 0">
      and ROWNUM &lt;= #{limit,jdbcType=DECIMAL}
    </if>
  </select>
  
  <update id="batchUpdateProcessFlag">
    update CM_PHONE_VALIDATION
    set PROCESS_FLAG = #{processFlag,jdbcType=DECIMAL},
        UPDATE_TIME = SYSDATE
    where ID in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id,jdbcType=DECIMAL}
    </foreach>
  </update>
  
  <update id="batchUpdateValidationResult">
    <foreach collection="records" item="record" separator=";">
      update CM_PHONE_VALIDATION
      set CHECK_RESULT = #{record.checkResult,jdbcType=DECIMAL},
          PROCESS_FLAG = #{record.processFlag,jdbcType=DECIMAL},
          PROCESS_REMARK = #{record.processRemark,jdbcType=VARCHAR},
          AREA = #{record.area,jdbcType=VARCHAR},
          OPERATOR_TYPE = #{record.operatorType,jdbcType=VARCHAR},
          UPDATE_TIME = SYSDATE
      where ID = #{record.id,jdbcType=DECIMAL}
    </foreach>
  </update>
  
  <update id="updateValidationResultById" parameterType="com.howbuy.cs.task.model.CmPhoneValidation">
    update CM_PHONE_VALIDATION
    set CHECK_RESULT = #{checkResult,jdbcType=DECIMAL},
        PROCESS_FLAG = #{processFlag,jdbcType=DECIMAL},
        PROCESS_REMARK = #{processRemark,jdbcType=VARCHAR},
        AREA = #{area,jdbcType=VARCHAR},
        OPERATOR_TYPE = #{operatorType,jdbcType=VARCHAR},
        UPDATE_TIME = SYSDATE
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
</mapper>
