/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.crm.mobile;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.export.client.facade.mobile.BatchMobileCheckFacade;
import com.howbuy.crm.export.client.domain.request.mobile.BatchMobileCheckRequest;
import com.howbuy.crm.export.client.domain.response.mobile.BatchMobileCheckResponse;
import com.howbuy.crm.export.client.domain.response.Response;
import com.howbuy.cs.outservice.crm.mobile.domain.BatchMobileCheckDTO;
import com.howbuy.cs.outservice.crm.mobile.domain.MobileCheckResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: CRM系统批量手机号验证外部接口封装服务
 * <AUTHOR>
 * @date 2025-09-08 14:20:17
 * @since JDK 1.8
 */
@Slf4j
@Service
public class BatchMobileCheckOuterService {

    /**
     * 成功返回码
     */
    private static final String SUCCESS_CODE = "SUCCESS";

    /**
     * 查询类型：普通手机号
     */
    private static final Integer TYPE_NORMAL = 0;

    /**
     * 查询类型：MD5(32位小写)
     */
    private static final Integer TYPE_MD5 = 1;

    @Autowired
    private BatchMobileCheckFacade batchMobileCheckFacade;

    /**
     * @description: 批量验证手机号状态
     * @param mobileList 手机号列表
     * @param type 查询类型 1：MD5(32位小写)，0：普通手机号；默认0
     * @return BatchMobileCheckDTO 批量验证结果
     * <AUTHOR>
     * @date 2025-09-08 14:20:17
     * @since JDK 1.8
     */
    public BatchMobileCheckDTO batchCheckMobile(List<String> mobileList, Integer type) {
        // 参数校验
        if (CollectionUtils.isEmpty(mobileList)) {
            throw new BusinessException("", "手机号列表不能为空");
        }

        // 默认查询类型为普通手机号
        if (type == null) {
            type = TYPE_NORMAL;
        }

        try {
            // 构建外部接口请求对象
            BatchMobileCheckRequest request = new BatchMobileCheckRequest();
            request.setMobileList(mobileList);
            request.setType(type);

            log.info("调用外部接口开始，method={}, request={}",
                "batchCheckMobile", JSON.toJSONString(request));

            // 调用外部接口
            Response<BatchMobileCheckResponse> responseWrapper = batchMobileCheckFacade.execute(request);

            // 响应结果校验
            if (responseWrapper == null || !isSuccess(responseWrapper)) {
                log.error("外部接口调用失败，request={}, response={}",
                    JSON.toJSONString(request), JSON.toJSONString(responseWrapper));
                throw new BusinessException("", "批量手机号验证接口调用失败");
            }

            log.info("调用外部接口结束，method={}, response={}",
                "batchCheckMobile", JSON.toJSONString(responseWrapper));

            // 转换为内部DTO对象
            return convertToBatchMobileCheckDTO(responseWrapper.getData());

        } catch (Exception e) {
            log.error("外部接口调用异常，mobileList={}, type={}, error={}",
                JSON.toJSONString(mobileList), type, e.getMessage(), e);
            throw new BusinessException("", "批量手机号验证接口调用异常");
        }
    }

    /**
     * @description: 外部响应对象转换为内部DTO
     * @param response 外部响应对象
     * @return BatchMobileCheckDTO 内部DTO对象
     * <AUTHOR>
     * @date 2025-09-08 14:20:17
     * @since JDK 1.8
     */
    private BatchMobileCheckDTO convertToBatchMobileCheckDTO(BatchMobileCheckResponse response) {
        BatchMobileCheckDTO dto = new BatchMobileCheckDTO();

        if (response != null) {
            // 转换验证结果列表
            if (CollectionUtils.isNotEmpty(response.getCheckResults())) {
                List<MobileCheckResultDTO> checkResults = new ArrayList<>();
                for (Object resultObj : response.getCheckResults()) {
                    // 根据实际的结果对象类型进行转换
                    MobileCheckResultDTO resultDTO = new MobileCheckResultDTO();
                    // 这里需要根据实际的结果对象属性进行转换
                    // 暂时使用反射或者JSON转换的方式
                    String jsonStr = JSON.toJSONString(resultObj);
                    MobileCheckResultDTO tempResult = JSON.parseObject(jsonStr, MobileCheckResultDTO.class);
                    if (tempResult != null) {
                        resultDTO.setMobile(tempResult.getMobile());
                        resultDTO.setArea(tempResult.getArea());
                        resultDTO.setNumberType(tempResult.getNumberType());
                        resultDTO.setStatus(tempResult.getStatus());
                        resultDTO.setStatusDesc(tempResult.getStatusDesc());
                        resultDTO.setLastTime(tempResult.getLastTime());
                    }
                    checkResults.add(resultDTO);
                }
                dto.setCheckResults(checkResults);
            }

            // 转换统计信息
            dto.setTotalCount(response.getTotalCount());
            dto.setSuccessCount(response.getSuccessCount());
            dto.setFailCount(response.getFailCount());
        }

        return dto;
    }

    /**
     * @description: 校验外部接口响应是否成功
     * @param responseWrapper 外部响应包装对象
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2025-09-08 14:20:17
     * @since JDK 1.8
     */
    private boolean isSuccess(Response<BatchMobileCheckResponse> responseWrapper) {
        return responseWrapper != null && SUCCESS_CODE.equals(responseWrapper.getCode());
    }


}
