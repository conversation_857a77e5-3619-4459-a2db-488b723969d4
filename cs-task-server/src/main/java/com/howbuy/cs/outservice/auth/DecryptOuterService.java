/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.auth;

import com.alibaba.fastjson.JSON;
import com.howbuy.auth.facade.decrypt.DecryptBatchFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: 认证系统批量解密外部接口封装服务
 * <AUTHOR>
 * @date 2025-09-08 14:20:17
 * @since JDK 1.8
 */
@Slf4j
@Service
public class DecryptOuterService {

    /**
     * 成功返回码
     */
    private static final String SUCCESS_CODE = "0000000";

    @Autowired
    private DecryptBatchFacade decryptBatchFacade;

    /**
     * @description: 批量解密手机号
     * @param encryptedList 加密信息列表
     * @return Map<String, String> 明文映射集合，key：密文，value：明文
     * <AUTHOR>
     * @date 2025-09-08 14:20:17
     * @since JDK 1.8
     */
    public Map<String, String> batchDecrypt(List<String> encryptedList) {
        // 参数校验
        if (CollectionUtils.isEmpty(encryptedList)) {
            throw new BusinessException("", "加密信息列表不能为空");
        }

        try {
            log.info("调用外部接口开始，method={}, request={}", 
                "batchDecrypt", JSON.toJSONString(encryptedList));

            // 调用外部接口
            CodecBatchResponse response = decryptBatchFacade.decryptBatch(encryptedList);

            // 响应结果校验
            if (response == null || !isSuccess(response)) {
                log.error("外部接口调用失败，request={}, response={}", 
                    JSON.toJSONString(encryptedList), JSON.toJSONString(response));
                throw new BusinessException("", "批量解密接口调用失败");
            }

            log.info("调用外部接口结束，method={}, response={}", 
                "batchDecrypt", JSON.toJSONString(response));

            // 返回解密结果映射
            return response.getCodecMap();

        } catch (Exception e) {
            log.error("外部接口调用异常，request={}, error={}", 
                JSON.toJSONString(encryptedList), e.getMessage(), e);
            throw new BusinessException("", "批量解密接口调用异常");
        }
    }

    /**
     * @description: 校验外部接口响应是否成功
     * @param response 外部响应对象
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2025-09-08 14:20:17
     * @since JDK 1.8
     */
    private boolean isSuccess(CodecBatchResponse response) {
        return response != null && SUCCESS_CODE.equals(response.getRetCode());
    }
}
