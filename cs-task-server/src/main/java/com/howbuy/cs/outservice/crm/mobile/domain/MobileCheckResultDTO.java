/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.crm.mobile.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 手机号验证结果数据传输对象
 * <AUTHOR>
 * @date 2025-09-08 14:20:17
 * @since JDK 1.8
 */
@Getter
@Setter
public class MobileCheckResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 手机号所属区域
     */
    private String area;

    /**
     * 手机号运营商类型
     */
    private String numberType;

    /**
     * 检测结果，枚举值： 0：空号 1：实号 2：停机 3：库无 4：沉默号 5：风险号
     */
    private Integer status;

    /**
     * 检测结果描述
     */
    private String statusDesc;

    /**
     * 时间戳，毫秒
     */
    private String lastTime;
}
